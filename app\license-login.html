<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم في التراخيص</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="icon" type="image/png" href="assets/future-fuel-icon (8).png">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #22c55e;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --bg-color: #ffffff;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            padding: 2rem;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .login-visual {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: white;
            text-align: center;
        }

        .visual-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .visual-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .visual-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            line-height: 1.6;
        }

        .login-form-container {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .form-header h1 {
            color: var(--text-color);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: var(--text-color);
            opacity: 0.7;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 2rem;
            background: var(--light-color);
            border-radius: 12px;
            padding: 0.25rem;
        }

        .tab-button {
            flex: 1;
            padding: 0.75rem;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            color: var(--text-color);
        }

        .tab-button.active {
            background: white;
            color: var(--primary-color);
            box-shadow: var(--shadow);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .input-container {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            padding-right: 3rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .input-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-color);
            opacity: 0.5;
        }

        .password-toggle {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-color);
            opacity: 0.5;
            cursor: pointer;
            padding: 0.25rem;
        }

        .password-toggle:hover {
            opacity: 0.8;
        }

        .login-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .quick-access {
            background: var(--light-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .quick-access h3 {
            color: var(--text-color);
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .quick-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
        }

        .quick-btn {
            padding: 0.75rem;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-color);
        }

        .quick-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .error-message {
            background: #fef2f2;
            color: var(--danger-color);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #fecaca;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            color: var(--success-color);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #bbf7d0;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }

        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 400px;
            }
            
            .login-visual {
                display: none;
            }
            
            .login-form-container {
                padding: 2rem;
            }
            
            .quick-buttons {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        .login-container {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Visual Side -->
        <div class="login-visual">
            <div class="visual-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2 class="visual-title">لوحة التحكم في التراخيص</h2>
            <p class="visual-subtitle">
                إدارة شاملة وآمنة لجميع تراخيص ورشات الغاز
                <br>
                مع نظام مراقبة متقدم وتقارير تفصيلية
            </p>
        </div>

        <!-- Form Side -->
        <div class="login-form-container">
            <div class="form-header">
                <h1>تسجيل الدخول</h1>
                <p>أدخل بياناتك للوصول إلى لوحة التحكم</p>
            </div>

            <!-- Login Tabs -->
            <div class="login-tabs">
                <button class="tab-button active" onclick="switchTab('admin')">
                    <i class="fas fa-user-shield"></i>
                    مدير النظام
                </button>
                <button class="tab-button" onclick="switchTab('developer')">
                    <i class="fas fa-code"></i>
                    المطور
                </button>
            </div>

            <!-- Error/Success Messages -->
            <div id="error-message" class="error-message"></div>
            <div id="success-message" class="success-message"></div>

            <!-- Admin Login Form -->
            <form id="admin-login-form" class="login-form">
                <div class="form-group">
                    <label for="admin-username">اسم المستخدم:</label>
                    <div class="input-container">
                        <input type="text" id="admin-username" placeholder="أدخل اسم المستخدم" required>
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="admin-password">كلمة المرور:</label>
                    <div class="input-container">
                        <input type="password" id="admin-password" placeholder="أدخل كلمة المرور" required>
                        <i class="fas fa-lock input-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('admin-password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="login-button" id="admin-login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- Developer Login Form -->
            <form id="developer-login-form" class="login-form" style="display: none;">
                <div class="form-group">
                    <label for="developer-key">مفتاح المطور:</label>
                    <div class="input-container">
                        <input type="text" id="developer-key" placeholder="أدخل مفتاح المطور" required>
                        <i class="fas fa-key input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="developer-password">كلمة مرور المطور:</label>
                    <div class="input-container">
                        <input type="password" id="developer-password" placeholder="أدخل كلمة مرور المطور" required>
                        <i class="fas fa-shield-alt input-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword('developer-password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="login-button" id="developer-login-btn">
                    <i class="fas fa-code"></i>
                    دخول المطور
                </button>
            </form>

            <!-- Forgot Password -->
            <div class="forgot-password">
                <a href="#" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
            </div>

            <!-- Quick Access -->
            <div class="quick-access">
                <h3><i class="fas fa-bolt"></i> وصول سريع</h3>
                <div class="quick-buttons">
                    <button class="quick-btn" onclick="quickLogin('admin')">
                        <i class="fas fa-user"></i><br>
                        مدير تجريبي
                    </button>
                    <button class="quick-btn" onclick="quickLogin('developer')">
                        <i class="fas fa-code"></i><br>
                        مطور تجريبي
                    </button>
                    <button class="quick-btn" onclick="showHelp()">
                        <i class="fas fa-question-circle"></i><br>
                        المساعدة
                    </button>
                    <button class="quick-btn" onclick="showSystemInfo()">
                        <i class="fas fa-info-circle"></i><br>
                        معلومات النظام
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/license-login.js"></script>
</body>
</html>
