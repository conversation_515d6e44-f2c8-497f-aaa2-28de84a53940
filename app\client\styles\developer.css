/* ==================== متغيرات CSS ==================== */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --bg-color: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1e293b;
    --text-color: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* ==================== إعدادات عامة ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-color);
    line-height: 1.6;
}

/* ==================== لوحة التحكم الرئيسية ==================== */
.developer-dashboard {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ==================== الشريط العلوي ==================== */
.dashboard-header {
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 2rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.system-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.version {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
}

.status.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

/* ==================== المحتوى الرئيسي ==================== */
.dashboard-main {
    display: flex;
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

/* ==================== الشريط الجانبي ==================== */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-color);
    border-left: 1px solid var(--border-color);
    position: fixed;
    top: var(--header-height);
    right: 0;
    height: calc(100vh - var(--header-height));
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-item a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-item a:hover,
.nav-item.active a {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    border-right-color: var(--primary-color);
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* ==================== منطقة المحتوى ==================== */
.content-area {
    flex: 1;
    margin-right: var(--sidebar-width);
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
}

/* ==================== بطاقات الإحصائيات ==================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
}

.stat-icon.workshops {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-icon.active {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-icon.expired {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-icon.users {
    background: linear-gradient(135deg, var(--info-color), #0891b2);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ==================== الرسوم البيانية ==================== */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: var(--bg-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.chart-card h3 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-size: 1.2rem;
}

.license-distribution {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.license-type {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.license-label {
    min-width: 80px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.license-bar {
    flex: 1;
    height: 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
    overflow: hidden;
}

.license-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}

.license-fill.trial {
    background: linear-gradient(90deg, var(--warning-color), #d97706);
}

.license-fill.professional {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.license-fill.enterprise {
    background: linear-gradient(90deg, var(--success-color), #059669);
}

.license-count {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--text-color);
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: var(--text-color);
}

.activity-content p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* ==================== الجداول ==================== */
.table-container {
    background: var(--bg-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--bg-secondary);
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.data-table tr:hover {
    background: var(--bg-secondary);
}

/* ==================== مولد التراخيص ==================== */
.license-generator {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.generator-card {
    background: var(--bg-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.generator-card h3 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-color);
    color: var(--text-color);
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.generated-license {
    background: var(--bg-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.generated-license h3 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.license-key {
    margin-bottom: 1rem;
}

.license-key label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.key-display {
    display: flex;
    gap: 0.5rem;
}

.key-display input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-color);
    font-family: monospace;
    font-size: 0.9rem;
}

.license-info p {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.license-info strong {
    color: var(--text-secondary);
}

/* ==================== الأزرار ==================== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* ==================== النوافذ المنبثقة ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-color);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: var(--bg-secondary);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    border-radius: 16px 16px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

.modal-body {
    padding: 2rem;
}

/* ==================== رسائل التنبيه ==================== */
.toast-container {
    position: fixed;
    top: 90px;
    right: 20px;
    z-index: 3000;
}

.toast {
    background: var(--bg-color);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ==================== تنسيقات الشاشات الصغيرة ==================== */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .content-area {
        margin-right: 0;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .license-generator {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

/* ==================== إضافات خاصة ==================== */
.backup-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.backup-content {
    background: var(--bg-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-badge.trial {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.workshop-code {
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.license-key-display {
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    word-break: break-all;
    border: 1px solid var(--border-color);
}
