@echo off
chcp 65001 >nul
title نظام إدارة محطة الغاز - النسخة المحدثة v2.0

color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                نظام إدارة محطة الغاز المحدث                ║
echo ║                      النسخة 2.0                            ║
echo ║                    Build: 2024.12.001                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 مرحباً بك في النظام المحدث!
echo 📋 تم تنظيف النظام وترك النسخ المحدثة فقط
echo.

echo ┌─────────────────── 🎯 خيارات التشغيل ──────────────────────┐
echo │                                                              │
echo │  1️⃣  النظام الرئيسي (البرنامج الأساسي)                    │
echo │  2️⃣  نظام العميل (محدث)                                   │
echo │  3️⃣  لوحة التحكم في التراخيص                              │
echo │  4️⃣  خادم التفعيل                                          │
echo │  5️⃣  لوحة المطور                                           │
echo │  6️⃣  عرض معلومات النظام                                    │
echo │  0️⃣  خروج                                                  │
echo │                                                              │
echo └──────────────────────────────────────────────────────────────┘
echo.

set /p choice="اختر الرقم المناسب: "

if "%choice%"=="1" goto main_system
if "%choice%"=="2" goto client_system
if "%choice%"=="3" goto license_panel
if "%choice%"=="4" goto activation_server
if "%choice%"=="5" goto developer_panel
if "%choice%"=="6" goto system_info
if "%choice%"=="0" goto exit
goto invalid_choice

:main_system
echo.
echo 🏠 تشغيل النظام الرئيسي...
start "" "index.html"
goto success

:license_panel
echo.
echo 🛡️ تشغيل لوحة التحكم في التراخيص...
start "" "license-login.html"
goto success

:client_system
echo.
echo 👤 تشغيل نظام العميل...
start "" "client\index.html"
goto success

:activation_server
echo.
echo 🔧 تشغيل خادم التفعيل...
start "" "activation-server.html"
goto success

:developer_panel
echo.
echo 👨‍💻 تشغيل لوحة المطور...
start "" "developer.html"
goto success

:system_info
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات النظام                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📦 اسم النظام: نظام إدارة محطة الغاز
echo 🏷️ الإصدار: 2.0.0
echo 🏗️ رقم البناء: 2024.12.001
echo 📅 تاريخ التحديث: ديسمبر 2024
echo 👨‍💻 المطور: فريق تطوير الأنظمة
echo.
echo ┌─────────────────── 📁 الملفات المتاحة ─────────────────────┐
echo │                                                              │
echo │  ✅ index.html - واجهة النظام الرئيسي                      │
echo │  ✅ main.html - البرنامج الأساسي                           │
echo │  ✅ license-login.html - واجهة لوحة التحكم                 │
echo │  ✅ license-control-panel.html - لوحة التحكم               │
echo │  ✅ client/index.html - نظام العميل                       │
echo │  ✅ activation-server.html - خادم التفعيل                   │
echo │  ✅ developer.html - لوحة المطور                           │
echo │                                                              │
echo └──────────────────────────────────────────────────────────────┘
echo.
echo ┌─────────────────── 🗑️ الملفات المحذوفة ──────────────────────┐
echo │                                                              │
echo │  ❌ login.html - واجهة قديمة                                │
echo │  ❌ login-client.html - واجهة عميل قديمة                    │
echo │  ❌ logout-developer.html - صفحة خروج قديمة                 │
echo │  ❌ scripts/login.js - سكريبت قديم                          │
echo │  ❌ styles/login.css - أنماط قديمة                          │
echo │                                                              │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause
goto menu

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 📱 تحقق من المتصفح للوصول للنظام
echo.
pause
goto menu

:invalid_choice
echo.
echo ❌ اختيار غير صحيح! يرجى اختيار رقم من 0 إلى 6
echo.
pause
goto menu

:menu
cls
goto start

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة محطة الغاز!
echo 🌟 النظام محدث ومنظف بالكامل
echo.
pause
exit

:start
echo.
echo ┌─────────────────── 🎯 خيارات التشغيل ──────────────────────┐
echo │                                                              │
echo │  1️⃣  النظام الرئيسي (البرنامج الأساسي)                    │
echo │  2️⃣  نظام العميل (محدث)                                   │
echo │  3️⃣  لوحة التحكم في التراخيص                              │
echo │  4️⃣  خادم التفعيل                                          │
echo │  5️⃣  لوحة المطور                                           │
echo │  6️⃣  عرض معلومات النظام                                    │
echo │  0️⃣  خروج                                                  │
echo │                                                              │
echo └──────────────────────────────────────────────────────────────┘
echo.

set /p choice="اختر الرقم المناسب: "

if "%choice%"=="1" goto main_system
if "%choice%"=="2" goto client_system
if "%choice%"=="3" goto license_panel
if "%choice%"=="4" goto activation_server
if "%choice%"=="5" goto developer_panel
if "%choice%"=="6" goto system_info
if "%choice%"=="0" goto exit
goto invalid_choice
