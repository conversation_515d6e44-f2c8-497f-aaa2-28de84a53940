@echo off
chcp 65001 >nul
title نظام إدارة محطة الغاز - تشغيل سريع

color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  نظام إدارة محطة الغاز                     ║
echo ║                     تشغيل سريع                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 جاري تشغيل النظام الرئيسي...
timeout /t 1 /nobreak >nul

if not exist "index.html" (
    echo ❌ خطأ: ملف النظام الرئيسي غير موجود
    echo 📁 تأكد من وجود الملف: index.html
    pause
    exit
)

echo ✅ تم العثور على الملف
echo 🌐 فتح النظام في المتصفح...

start "" "index.html"

if %errorlevel% neq 0 (
    echo ❌ خطأ في فتح المتصفح
    pause
    exit
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                ✅ تم تشغيل النظام بنجاح!                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 المدير: admin / admin123
echo    👥 المستخدم: user / user123
echo.
echo 💡 استخدم أزرار الوصول السريع في الواجهة
echo.

timeout /t 3 /nobreak >nul
exit
