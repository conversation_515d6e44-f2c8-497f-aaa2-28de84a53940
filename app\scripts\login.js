// ==================== نظام تسجيل الدخول والتراخيص ====================

// بيانات النظام
let systemData = {
    workshops: JSON.parse(localStorage.getItem('systemWorkshops')) || [],
    licenses: JSON.parse(localStorage.getItem('systemLicenses')) || [],
    currentSession: null,
    developerKey: 'DEV-2024-GASMANAGEMENT-MASTER',
    developerPassword: 'GasSystem@2024!',
    systemVersion: '1.0.0',
    lastUpdate: new Date().toISOString()
};

// إعدادات النظام
const systemConfig = {
    trialPeriod: 30, // أيام
    maxUsers: 10,
    features: {
        basic: ['customers', 'cards', 'appointments'],
        professional: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports'],
        enterprise: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports', 'employees', 'payroll', 'maintenance']
    }
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
    setupEventListeners();
    checkLicenseStatus();
});

// تهيئة النظام
function initializeSystem() {
    console.log('🚀 تهيئة نظام إدارة محطة الغاز...');
    
    // إنشاء ورشة تجريبية إذا لم تكن موجودة
    if (systemData.workshops.length === 0) {
        createDemoWorkshop();
    }
    
    // تحديث حالة الترخيص
    updateLicenseStatus();
}

// إنشاء ورشة تجريبية
function createDemoWorkshop() {
    const demoWorkshop = {
        id: generateId(),
        code: 'DEMO-001',
        name: 'ورشة تجريبية',
        owner: 'مدير النظام',
        phone: '**********',
        address: 'الجزائر العاصمة',
        email: '<EMAIL>',
        licenseType: 'trial',
        licenseKey: generateLicenseKey(),
        expiryDate: new Date(Date.now() + systemConfig.trialPeriod * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true,
        users: [
            {
                id: generateId(),
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                permissions: systemConfig.features.enterprise,
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ],
        createdAt: new Date().toISOString(),
        lastLogin: null
    };
    
    systemData.workshops.push(demoWorkshop);
    saveSystemData();
    
    console.log('✅ تم إنشاء ورشة تجريبية:', demoWorkshop.code);
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج تسجيل الدخول
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // نموذج استعادة كلمة المرور
    const forgotPasswordForm = document.getElementById('forgot-password-form');
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }
    
    // نموذج لوحة المطور
    const developerForm = document.getElementById('developer-login-form');
    if (developerForm) {
        developerForm.addEventListener('submit', handleDeveloperLogin);
    }
}

// معالجة تسجيل الدخول
function handleLogin(e) {
    e.preventDefault();
    
    const workshopCode = document.getElementById('workshop-code').value.trim();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    if (!workshopCode || !username || !password) {
        showToast('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    // البحث عن الورشة
    const workshop = systemData.workshops.find(w => w.code === workshopCode && w.isActive);
    if (!workshop) {
        showToast('كود الورشة غير صحيح أو غير مفعل', 'error');
        return;
    }
    
    // التحقق من صلاحية الترخيص
    if (!isLicenseValid(workshop)) {
        showToast('انتهت صلاحية ترخيص الورشة. يرجى التواصل مع المطور', 'error');
        return;
    }
    
    // البحث عن المستخدم
    const user = workshop.users.find(u => u.username === username && u.isActive);
    if (!user) {
        showToast('اسم المستخدم غير موجود أو غير مفعل', 'error');
        return;
    }
    
    // التحقق من كلمة المرور
    if (user.password !== password) {
        showToast('كلمة المرور غير صحيحة', 'error');
        return;
    }
    
    // تسجيل دخول ناجح
    const session = {
        workshopId: workshop.id,
        workshopCode: workshop.code,
        workshopName: workshop.name,
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        licenseType: workshop.licenseType,
        loginTime: new Date().toISOString(),
        rememberMe: rememberMe
    };
    
    // حفظ الجلسة
    systemData.currentSession = session;
    workshop.lastLogin = new Date().toISOString();
    
    if (rememberMe) {
        localStorage.setItem('gasSystemSession', JSON.stringify(session));
    } else {
        sessionStorage.setItem('gasSystemSession', JSON.stringify(session));
    }
    
    saveSystemData();
    
    showToast('تم تسجيل الدخول بنجاح', 'success');
    
    // الانتقال للنظام الرئيسي
    setTimeout(() => {
        window.location.href = 'main.html';
    }, 1500);
}

// معالجة استعادة كلمة المرور
function handleForgotPassword(e) {
    e.preventDefault();
    
    const workshopCode = document.getElementById('recovery-workshop-code').value.trim();
    const email = document.getElementById('recovery-email').value.trim();
    
    const workshop = systemData.workshops.find(w => w.code === workshopCode);
    if (!workshop || workshop.email !== email) {
        showToast('بيانات الورشة أو البريد الإلكتروني غير صحيح', 'error');
        return;
    }
    
    // محاكاة إرسال رابط الاستعادة
    showToast('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
    hideForgotPassword();
}

// معالجة دخول لوحة المطور
function handleDeveloperLogin(e) {
    e.preventDefault();
    
    const developerKey = document.getElementById('developer-key').value;
    const developerPassword = document.getElementById('developer-password').value;
    
    if (developerKey !== systemData.developerKey || developerPassword !== systemData.developerPassword) {
        showToast('مفتاح المطور أو كلمة المرور غير صحيحة', 'error');
        return;
    }
    
    showToast('تم تسجيل الدخول للوحة المطور', 'success');
    hideDeveloperPanel();
    
    // الانتقال للوحة المطور
    setTimeout(() => {
        window.location.href = 'developer.html';
    }, 1500);
}

// التحقق من صلاحية الترخيص
function isLicenseValid(workshop) {
    if (!workshop.expiryDate) return false;
    
    const expiryDate = new Date(workshop.expiryDate);
    const now = new Date();
    
    return expiryDate > now;
}

// تحديث حالة الترخيص
function updateLicenseStatus() {
    const statusElement = document.getElementById('license-status');
    if (!statusElement) return;
    
    const activeWorkshops = systemData.workshops.filter(w => w.isActive && isLicenseValid(w));
    
    if (activeWorkshops.length > 0) {
        statusElement.textContent = `${activeWorkshops.length} ورشة مرخصة ونشطة`;
        statusElement.style.color = 'var(--success-color)';
    } else {
        statusElement.textContent = 'لا توجد تراخيص نشطة';
        statusElement.style.color = 'var(--danger-color)';
    }
}

// فحص حالة الترخيص
function checkLicenseStatus() {
    // فحص الجلسة المحفوظة
    const savedSession = localStorage.getItem('gasSystemSession') || sessionStorage.getItem('gasSystemSession');
    
    if (savedSession) {
        try {
            const session = JSON.parse(savedSession);
            const workshop = systemData.workshops.find(w => w.id === session.workshopId);
            
            if (workshop && isLicenseValid(workshop)) {
                // جلسة صالحة، الانتقال للنظام
                systemData.currentSession = session;
                window.location.href = 'main.html';
                return;
            } else {
                // جلسة منتهية الصلاحية
                localStorage.removeItem('gasSystemSession');
                sessionStorage.removeItem('gasSystemSession');
            }
        } catch (error) {
            console.error('خطأ في قراءة الجلسة المحفوظة:', error);
        }
    }
    
    updateLicenseStatus();
}

// توليد معرف فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// توليد مفتاح ترخيص
function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        if (i > 0 && i % 4 === 0) result += '-';
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// حفظ بيانات النظام
function saveSystemData() {
    localStorage.setItem('systemWorkshops', JSON.stringify(systemData.workshops));
    localStorage.setItem('systemLicenses', JSON.stringify(systemData.licenses));
}

// إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleBtn.className = 'fas fa-eye';
    }
}

// إظهار نافذة استعادة كلمة المرور
function showForgotPassword() {
    document.getElementById('forgot-password-modal').style.display = 'flex';
}

// إخفاء نافذة استعادة كلمة المرور
function hideForgotPassword() {
    document.getElementById('forgot-password-modal').style.display = 'none';
}

// إظهار نافذة الدعم
function showSupport() {
    document.getElementById('support-modal').style.display = 'flex';
}

// إخفاء نافذة الدعم
function hideSupport() {
    document.getElementById('support-modal').style.display = 'none';
}

// إظهار نافذة لوحة المطور
function showDeveloperPanel() {
    document.getElementById('developer-panel-modal').style.display = 'flex';
}

// إخفاء نافذة لوحة المطور
function hideDeveloperPanel() {
    document.getElementById('developer-panel-modal').style.display = 'none';
}

// إظهار رسالة تنبيه
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = type === 'success' ? 'fas fa-check-circle' : 
                 type === 'error' ? 'fas fa-exclamation-circle' : 
                 type === 'warning' ? 'fas fa-exclamation-triangle' : 
                 'fas fa-info-circle';
    
    toast.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
    `;
    
    toastContainer.appendChild(toast);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});

// منع إرسال النماذج الفارغة
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.tagName === 'INPUT') {
        const form = e.target.closest('form');
        if (form) {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }
    }
});

console.log('✅ تم تحميل نظام تسجيل الدخول بنجاح');

// ==================== دوال إضافية للنظام ====================

// تصدير بيانات النظام للمطور
function exportSystemData() {
    const exportData = {
        workshops: systemData.workshops,
        licenses: systemData.licenses,
        systemVersion: systemData.systemVersion,
        exportDate: new Date().toISOString(),
        totalWorkshops: systemData.workshops.length,
        activeWorkshops: systemData.workshops.filter(w => w.isActive).length,
        expiredLicenses: systemData.workshops.filter(w => !isLicenseValid(w)).length
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `gas-system-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
}

// استيراد بيانات النظام
function importSystemData(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);

            if (importedData.workshops && Array.isArray(importedData.workshops)) {
                systemData.workshops = importedData.workshops;
                saveSystemData();
                showToast('تم استيراد البيانات بنجاح', 'success');
                updateLicenseStatus();
            } else {
                showToast('ملف البيانات غير صالح', 'error');
            }
        } catch (error) {
            showToast('خطأ في قراءة ملف البيانات', 'error');
        }
    };
    reader.readAsText(file);
}

// إنشاء نسخة احتياطية
function createBackup() {
    const backupData = {
        ...systemData,
        backupDate: new Date().toISOString(),
        backupVersion: systemData.systemVersion
    };

    const dataStr = JSON.stringify(backupData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `gas-system-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showToast('تم إنشاء النسخة الاحتياطية', 'success');
}

// إعادة تعيين النظام
function resetSystem() {
    if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع الورشات والتراخيص!')) {
            localStorage.clear();
            sessionStorage.clear();
            systemData = {
                workshops: [],
                licenses: [],
                currentSession: null,
                developerKey: 'DEV-2024-GASMANAGEMENT-MASTER',
                developerPassword: 'GasSystem@2024!',
                systemVersion: '1.0.0',
                lastUpdate: new Date().toISOString()
            };

            showToast('تم إعادة تعيين النظام', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
    }
}

// تحديث إعدادات النظام
function updateSystemSettings(newSettings) {
    Object.assign(systemConfig, newSettings);
    systemData.lastUpdate = new Date().toISOString();
    saveSystemData();
    showToast('تم تحديث إعدادات النظام', 'success');
}

// إحصائيات النظام
function getSystemStats() {
    const stats = {
        totalWorkshops: systemData.workshops.length,
        activeWorkshops: systemData.workshops.filter(w => w.isActive).length,
        expiredLicenses: systemData.workshops.filter(w => !isLicenseValid(w)).length,
        totalUsers: systemData.workshops.reduce((total, w) => total + w.users.length, 0),
        trialWorkshops: systemData.workshops.filter(w => w.licenseType === 'trial').length,
        professionalWorkshops: systemData.workshops.filter(w => w.licenseType === 'professional').length,
        enterpriseWorkshops: systemData.workshops.filter(w => w.licenseType === 'enterprise').length,
        lastLogin: systemData.workshops.reduce((latest, w) => {
            if (w.lastLogin && (!latest || new Date(w.lastLogin) > new Date(latest))) {
                return w.lastLogin;
            }
            return latest;
        }, null)
    };

    return stats;
}

// ==================== نظام طلب التفعيل عن بُعد ====================

// طلب تفعيل جديد
function requestActivation() {
    const modalBody = `
        <div class="activation-request-form">
            <h3>طلب تفعيل جديد</h3>
            <p>املأ النموذج أدناه لطلب تفعيل النظام من المطور</p>

            <form id="activation-request-form">
                <div class="form-group">
                    <label for="workshop-name">اسم الورشة: *</label>
                    <input type="text" id="workshop-name" required placeholder="أدخل اسم الورشة">
                </div>

                <div class="form-group">
                    <label for="owner-name">اسم المالك: *</label>
                    <input type="text" id="owner-name" required placeholder="أدخل اسم مالك الورشة">
                </div>

                <div class="form-group">
                    <label for="phone-number">رقم الهاتف: *</label>
                    <input type="tel" id="phone-number" required placeholder="**********">
                </div>

                <div class="form-group">
                    <label for="email-address">البريد الإلكتروني:</label>
                    <input type="email" id="email-address" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="workshop-address">عنوان الورشة:</label>
                    <input type="text" id="workshop-address" placeholder="العنوان التفصيلي للورشة">
                </div>

                <div class="form-group">
                    <label for="license-type-request">نوع الترخيص المطلوب:</label>
                    <select id="license-type-request">
                        <option value="trial">تجريبي (30 يوم) - مجاني</option>
                        <option value="professional">احترافي (سنة واحدة)</option>
                        <option value="enterprise">مؤسسي (بدون انتهاء)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="activation-code-input">كود التفعيل (إذا كان متوفراً):</label>
                    <input type="text" id="activation-code-input" placeholder="ACT-XXXX-XXXX-XXXX">
                </div>

                <div class="form-group">
                    <label for="request-notes">ملاحظات إضافية:</label>
                    <textarea id="request-notes" rows="3" placeholder="أي ملاحظات أو متطلبات خاصة..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        إرسال طلب التفعيل
                    </button>
                    <button type="button" class="btn btn-info" onclick="tryActivationCode()">
                        <i class="fas fa-key"></i>
                        تجربة كود التفعيل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideActivationRequest()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;

    showModal('طلب تفعيل النظام', modalBody);

    document.getElementById('activation-request-form').addEventListener('submit', handleActivationRequest);
}

// معالجة طلب التفعيل
function handleActivationRequest(e) {
    e.preventDefault();

    const requestData = {
        workshopName: document.getElementById('workshop-name').value.trim(),
        ownerName: document.getElementById('owner-name').value.trim(),
        phoneNumber: document.getElementById('phone-number').value.trim(),
        emailAddress: document.getElementById('email-address').value.trim(),
        workshopAddress: document.getElementById('workshop-address').value.trim(),
        licenseType: document.getElementById('license-type-request').value,
        activationCode: document.getElementById('activation-code-input').value.trim(),
        notes: document.getElementById('request-notes').value.trim()
    };

    if (!requestData.workshopName || !requestData.ownerName || !requestData.phoneNumber) {
        showToast('يرجى ملء الحقول المطلوبة', 'error');
        return;
    }

    // محاكاة إرسال الطلب للخادم
    simulateActivationRequest(requestData);
}

// محاكاة إرسال طلب التفعيل
function simulateActivationRequest(requestData) {
    showToast('جاري إرسال طلب التفعيل...', 'info');

    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // حفظ الطلب محلياً
        const request = {
            id: generateId(),
            ...requestData,
            status: 'pending',
            timestamp: new Date().toISOString(),
            clientIP: '192.168.1.' + Math.floor(Math.random() * 254 + 1)
        };

        // حفظ في التخزين المحلي
        const savedRequests = JSON.parse(localStorage.getItem('activationRequests')) || [];
        savedRequests.push(request);
        localStorage.setItem('activationRequests', JSON.stringify(savedRequests));

        hideActivationRequest();

        showToast('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً', 'success');

        // عرض معلومات المتابعة
        showFollowUpInfo(request);

    }, 2000);
}

// عرض معلومات المتابعة
function showFollowUpInfo(request) {
    const followUpHTML = `
        <div class="follow-up-info">
            <h3><i class="fas fa-check-circle text-success"></i> تم إرسال طلب التفعيل</h3>

            <div class="request-summary">
                <h4>ملخص الطلب:</h4>
                <div class="summary-details">
                    <div><strong>رقم الطلب:</strong> ${request.id}</div>
                    <div><strong>اسم الورشة:</strong> ${request.workshopName}</div>
                    <div><strong>نوع الترخيص:</strong> ${getLicenseTypeText(request.licenseType)}</div>
                    <div><strong>وقت الإرسال:</strong> ${new Date(request.timestamp).toLocaleString('ar-DZ')}</div>
                </div>
            </div>

            <div class="next-steps">
                <h4>الخطوات التالية:</h4>
                <ol>
                    <li>سيقوم المطور بمراجعة طلبك خلال 24 ساعة</li>
                    <li>ستتلقى كود التفعيل عبر الهاتف أو البريد الإلكتروني</li>
                    <li>استخدم كود التفعيل لتفعيل النظام</li>
                </ol>
            </div>

            <div class="contact-info">
                <h4>معلومات التواصل:</h4>
                <div class="contact-details">
                    <div><i class="fas fa-phone"></i> الهاتف: +213 555 123 456</div>
                    <div><i class="fas fa-envelope"></i> البريد: <EMAIL></div>
                    <div><i class="fas fa-clock"></i> ساعات العمل: السبت - الخميس (8:00 - 17:00)</div>
                </div>
            </div>

            <div class="follow-up-actions">
                <button class="btn btn-primary" onclick="checkActivationStatus('${request.id}')">
                    <i class="fas fa-search"></i>
                    تحقق من حالة الطلب
                </button>
                <button class="btn btn-info" onclick="requestActivation()">
                    <i class="fas fa-plus"></i>
                    طلب جديد
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('تأكيد إرسال الطلب', followUpHTML);
}

// تجربة كود التفعيل
function tryActivationCode() {
    const activationCode = document.getElementById('activation-code-input').value.trim();

    if (!activationCode) {
        showToast('يرجى إدخال كود التفعيل', 'error');
        return;
    }

    // محاكاة التحقق من كود التفعيل
    simulateActivationCodeCheck(activationCode);
}

// محاكاة التحقق من كود التفعيل
function simulateActivationCodeCheck(code) {
    showToast('جاري التحقق من كود التفعيل...', 'info');

    setTimeout(() => {
        // محاكاة أكواد صالحة
        const validCodes = [
            'ACT-TRIAL-123456',
            'ACT-PROFESSIONAL-789012',
            'ACT-ENTERPRISE-345678'
        ];

        if (validCodes.includes(code) || code.startsWith('ACT-')) {
            // كود صالح - إنشاء ورشة
            const licenseType = code.includes('TRIAL') ? 'trial' :
                              code.includes('PROFESSIONAL') ? 'professional' : 'enterprise';

            const workshopName = document.getElementById('workshop-name').value.trim() || 'ورشة جديدة';
            const ownerName = document.getElementById('owner-name').value.trim() || 'مالك الورشة';

            const workshop = createWorkshopFromActivation(code, licenseType, workshopName, ownerName);

            hideActivationRequest();
            showToast('تم تفعيل النظام بنجاح!', 'success');

            // عرض معلومات الورشة الجديدة
            showWorkshopInfo(workshop);

        } else {
            showToast('كود التفعيل غير صالح أو منتهي الصلاحية', 'error');
        }
    }, 2000);
}

// إنشاء ورشة من كود التفعيل
function createWorkshopFromActivation(activationCode, licenseType, workshopName, ownerName) {
    const workshop = {
        id: generateId(),
        code: generateWorkshopCode(),
        name: workshopName,
        owner: ownerName,
        phone: document.getElementById('phone-number')?.value || '',
        address: document.getElementById('workshop-address')?.value || '',
        email: document.getElementById('email-address')?.value || '',
        licenseType: licenseType,
        licenseKey: generateLicenseKey(),
        activationCode: activationCode,
        expiryDate: calculateExpiryDate(licenseType),
        isActive: true,
        users: [
            {
                id: generateId(),
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                permissions: getPermissionsByLicenseType(licenseType),
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ],
        createdAt: new Date().toISOString(),
        lastLogin: null
    };

    // إضافة الورشة للنظام
    systemData.workshops.push(workshop);
    saveSystemData();

    return workshop;
}

// حساب تاريخ انتهاء الترخيص
function calculateExpiryDate(licenseType) {
    const expiryDate = new Date();

    switch (licenseType) {
        case 'trial':
            expiryDate.setDate(expiryDate.getDate() + 30);
            break;
        case 'professional':
            expiryDate.setFullYear(expiryDate.getFullYear() + 1);
            break;
        case 'enterprise':
            expiryDate.setFullYear(expiryDate.getFullYear() + 10);
            break;
    }

    return expiryDate.toISOString();
}

// عرض معلومات الورشة الجديدة
function showWorkshopInfo(workshop) {
    const workshopInfoHTML = `
        <div class="workshop-info-display">
            <h3><i class="fas fa-check-circle text-success"></i> تم تفعيل النظام بنجاح!</h3>

            <div class="workshop-details">
                <h4>معلومات الورشة:</h4>
                <div class="details-grid">
                    <div><strong>كود الورشة:</strong> <span class="workshop-code">${workshop.code}</span></div>
                    <div><strong>اسم الورشة:</strong> ${workshop.name}</div>
                    <div><strong>نوع الترخيص:</strong> ${getLicenseTypeText(workshop.licenseType)}</div>
                    <div><strong>تاريخ الانتهاء:</strong> ${new Date(workshop.expiryDate).toLocaleDateString('ar-DZ')}</div>
                </div>
            </div>

            <div class="login-credentials">
                <h4>بيانات تسجيل الدخول:</h4>
                <div class="credentials-box">
                    <div><strong>كود الورشة:</strong> <code>${workshop.code}</code></div>
                    <div><strong>اسم المستخدم:</strong> <code>admin</code></div>
                    <div><strong>كلمة المرور:</strong> <code>admin123</code></div>
                </div>
                <p class="note">احفظ هذه المعلومات في مكان آمن</p>
            </div>

            <div class="workshop-actions">
                <button class="btn btn-success" onclick="loginWithNewWorkshop('${workshop.code}')">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول الآن
                </button>
                <button class="btn btn-info" onclick="copyWorkshopInfo('${workshop.code}')">
                    <i class="fas fa-copy"></i>
                    نسخ المعلومات
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('تفعيل ناجح', workshopInfoHTML);
}

// تسجيل دخول بالورشة الجديدة
function loginWithNewWorkshop(workshopCode) {
    document.getElementById('workshop-code').value = workshopCode;
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin123';

    hideModal();

    // محاكاة تسجيل الدخول
    setTimeout(() => {
        document.getElementById('login-form').dispatchEvent(new Event('submit'));
    }, 500);
}

// نسخ معلومات الورشة
function copyWorkshopInfo(workshopCode) {
    const workshop = systemData.workshops.find(w => w.code === workshopCode);
    if (!workshop) return;

    const info = `معلومات الورشة:
كود الورشة: ${workshop.code}
اسم المستخدم: admin
كلمة المرور: admin123
نوع الترخيص: ${getLicenseTypeText(workshop.licenseType)}
تاريخ الانتهاء: ${new Date(workshop.expiryDate).toLocaleDateString('ar-DZ')}`;

    navigator.clipboard.writeText(info).then(() => {
        showToast('تم نسخ معلومات الورشة', 'success');
    });
}

// التحقق من حالة طلب التفعيل
function checkActivationStatus(requestId) {
    const savedRequests = JSON.parse(localStorage.getItem('activationRequests')) || [];
    const request = savedRequests.find(r => r.id === requestId);

    if (!request) {
        showToast('لم يتم العثور على الطلب', 'error');
        return;
    }

    // محاكاة تحديث حالة الطلب
    const statuses = ['pending', 'approved', 'rejected'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

    const statusHTML = `
        <div class="status-check">
            <h3>حالة طلب التفعيل</h3>

            <div class="request-info">
                <div><strong>رقم الطلب:</strong> ${request.id}</div>
                <div><strong>اسم الورشة:</strong> ${request.workshopName}</div>
                <div><strong>تاريخ الطلب:</strong> ${new Date(request.timestamp).toLocaleDateString('ar-DZ')}</div>
            </div>

            <div class="status-display">
                <div class="status-badge ${randomStatus}">
                    ${randomStatus === 'pending' ? '⏳ قيد المراجعة' :
                      randomStatus === 'approved' ? '✅ تم الموافقة' : '❌ تم الرفض'}
                </div>

                ${randomStatus === 'approved' ? `
                    <div class="activation-code-display">
                        <h4>كود التفعيل:</h4>
                        <div class="code-box">
                            <code>ACT-${request.licenseType.toUpperCase()}-${Date.now().toString(36).toUpperCase()}</code>
                            <button class="btn btn-sm btn-secondary" onclick="copyText(this.previousElementSibling.textContent)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                ` : ''}
            </div>

            <div class="status-actions">
                <button class="btn btn-secondary" onclick="hideModal()">إغلاق</button>
            </div>
        </div>
    `;

    showModal('حالة الطلب', statusHTML);
}

// نسخ نص
function copyText(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('تم النسخ', 'success');
    });
}

// إخفاء طلب التفعيل
function hideActivationRequest() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// إظهار نافذة منبثقة
function showModal(title, content) {
    // إزالة النوافذ الموجودة
    const existingModals = document.querySelectorAll('.modal');
    existingModals.forEach(modal => modal.remove());

    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// إخفاء النافذة
function hideModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => modal.remove());
}
