/* ==================== متغيرات CSS ==================== */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --bg-color: #ffffff;
    --bg-secondary: #f8fafc;
    --text-color: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ==================== إعدادات عامة ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* ==================== حاوي تسجيل الدخول ==================== */
.login-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.background-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* ==================== بطاقة تسجيل الدخول ==================== */
.login-card {
    position: relative;
    z-index: 2;
    background: var(--bg-color);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 450px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.logo {
    position: relative;
    z-index: 1;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    backdrop-filter: blur(10px);
}

.login-header h1 {
    position: relative;
    z-index: 1;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.login-header p {
    position: relative;
    z-index: 1;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* ==================== نموذج تسجيل الدخول ==================== */
.login-form-container {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: var(--bg-color);
}

.toggle-password {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* ==================== خيارات النموذج ==================== */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-left: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--primary-dark);
}

/* ==================== زر تسجيل الدخول ==================== */
.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

/* ==================== تذييل تسجيل الدخول ==================== */
.login-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
}

.license-info {
    text-align: center;
    margin-bottom: 1rem;
}

.license-info p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.license-status {
    font-weight: 600;
    color: var(--success-color);
}

.support-links {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
}

.support-links a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.8rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.support-links a:hover {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

/* ==================== النوافذ المنبثقة ==================== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-color);
    border-radius: 16px;
    width: 90%;
    max-width: 400px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.modal-header {
    background: var(--bg-secondary);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

.modal-body {
    padding: 1.5rem;
}

/* ==================== أزرار ==================== */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
    transform: translateY(-1px);
}

/* ==================== معلومات الدعم ==================== */
.support-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.support-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.support-item i {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.support-item h4 {
    margin: 0 0 0.25rem 0;
    color: var(--text-color);
    font-size: 0.9rem;
}

.support-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* ==================== رسائل التنبيه ==================== */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
}

.toast {
    background: var(--bg-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ==================== تنسيقات الشاشات الصغيرة ==================== */
@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
        max-width: none;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-form-container {
        padding: 1.5rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .support-links {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-content {
        margin: 1rem;
        max-width: none;
    }
}

/* ==================== تحسينات جديدة ==================== */

/* مؤشر التحميل العام */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(10px);
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات النوافذ المنبثقة */
.modal.show {
    opacity: 1;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* تحسينات رسائل التنبيه */
.toast {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--bg-color);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    animation: toastSlideIn 0.3s ease-out;
    max-width: 400px;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: var(--primary-color);
}

.toast.success .toast-icon {
    background: var(--success-color);
}

.toast.error .toast-icon {
    background: var(--danger-color);
}

.toast.warning .toast-icon {
    background: var(--warning-color);
}

.toast-content {
    flex: 1;
    color: var(--text-color);
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.toast-close:hover {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات معلومات النسخة */
.version-info {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.version-info p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.build-info {
    font-family: 'Courier New', monospace;
    font-size: 0.7rem !important;
    opacity: 0.7;
}

/* تحسينات الخلفية المتحركة */
.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatAnimation 8s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: -10%;
    left: -10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -5%;
    animation-delay: 2s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    bottom: -5%;
    left: 30%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 20%;
    right: 20%;
    animation-delay: 6s;
}

@keyframes floatAnimation {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.1;
    }
    25% {
        transform: translateY(-30px) rotate(90deg) scale(1.1);
        opacity: 0.2;
    }
    50% {
        transform: translateY(-60px) rotate(180deg) scale(1);
        opacity: 0.15;
    }
    75% {
        transform: translateY(-30px) rotate(270deg) scale(0.9);
        opacity: 0.1;
    }
}

/* تحسينات المعلومات التجريبية */
.demo-info {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 10;
    max-width: 300px;
}

.demo-card {
    background: var(--bg-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.demo-card h3 {
    color: var(--text-color);
    font-size: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.demo-credentials {
    margin-bottom: 1rem;
}

.credential-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    font-size: 0.8rem;
}

.credential-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.credential-item .value {
    color: var(--text-color);
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.copy-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: rgba(37, 99, 235, 0.1);
}

.demo-login-btn {
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.demo-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}
