<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة محطة الغاز - نسخة العميل v2.0</title>
    <meta name="description" content="نظام إدارة شامل لورشات الغاز - إدارة العملاء، بطاقات الغاز، المواعيد والمزيد">
    <meta name="keywords" content="إدارة محطة الغاز, ورشة غاز, بطاقات غاز, نظام إدارة">
    <link rel="stylesheet" href="styles/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="icon" type="image/png" href="assets/future-fuel-icon (8).png">
</head>
<body>
    <div class="login-container">
        <!-- الخلفية المتحركة -->
        <div class="animated-background">
            <div class="floating-shape shape-1"></div>
            <div class="floating-shape shape-2"></div>
            <div class="floating-shape shape-3"></div>
            <div class="floating-shape shape-4"></div>
        </div>

        <!-- بطاقة تسجيل الدخول -->
        <div class="login-card">
            <!-- الشعار والعنوان -->
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1>نظام إدارة محطة الغاز</h1>
                <p>إدارة شاملة وموثوقة لورشة الغاز</p>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="workshop-code">
                        <i class="fas fa-building"></i>
                        كود الورشة
                    </label>
                    <input 
                        type="text" 
                        id="workshop-code" 
                        name="workshop-code" 
                        required 
                        placeholder="أدخل كود الورشة"
                        autocomplete="organization"
                    >
                </div>

                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        required 
                        placeholder="أدخل اسم المستخدم"
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required 
                            placeholder="أدخل كلمة المرور"
                            autocomplete="current-password"
                        >
                        <button type="button" class="toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="remember-me" name="remember-me">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" onclick="showPasswordRecovery()" class="forgot-password">
                        نسيت كلمة المرور؟
                    </a>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- روابط إضافية -->
            <div class="login-footer">
                <div class="divider">
                    <span>أو</span>
                </div>

                <div class="support-links">
                    <a href="#" onclick="requestActivation()">
                        <i class="fas fa-key"></i>
                        طلب تفعيل النظام
                    </a>
                    <a href="#" onclick="showSupport()">
                        <i class="fas fa-headset"></i>
                        الدعم الفني
                    </a>
                    <a href="#" onclick="showSystemInfo()">
                        <i class="fas fa-info-circle"></i>
                        معلومات النظام
                    </a>
                    <a href="#" onclick="checkActivationStatus()">
                        <i class="fas fa-search"></i>
                        حالة التفعيل
                    </a>
                </div>

                <!-- معلومات النسخة -->
                <div class="version-info">
                    <p>نسخة العميل v2.0 - محدثة</p>
                    <p>&copy; 2024 نظام إدارة محطة الغاز</p>
                    <p class="build-info">Build: <span id="build-number">2024.12.001</span></p>
                </div>
            </div>
        </div>

        <!-- معلومات تجريبية -->
        <div class="demo-info">
            <div class="demo-card">
                <h3><i class="fas fa-info-circle"></i> للتجربة السريعة</h3>
                <div class="demo-credentials">
                    <div class="credential-item">
                        <span class="label">كود الورشة:</span>
                        <span class="value">DEMO-001</span>
                        <button class="copy-btn" onclick="copyToClipboard('DEMO-001')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="credential-item">
                        <span class="label">المستخدم:</span>
                        <span class="value">admin</span>
                        <button class="copy-btn" onclick="copyToClipboard('admin')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="credential-item">
                        <span class="label">كلمة المرور:</span>
                        <span class="value">admin123</span>
                        <button class="copy-btn" onclick="copyToClipboard('admin123')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <button class="demo-login-btn" onclick="fillDemoCredentials()">
                    <i class="fas fa-play"></i>
                    تجربة النظام الآن
                </button>
            </div>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <div id="modal-overlay" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">عنوان النافذة</h3>
                <button class="close-btn" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- محتوى النافذة -->
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div id="toast-container" class="toast-container"></div>

    <!-- مؤشر التحميل العام -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>جاري التحميل...</h3>
            <p id="loading-message">يرجى الانتظار</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="scripts/login.js"></script>
    
    <script>
        // دوال إضافية للواجهة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('تم النسخ: ' + text, 'success');
            }).catch(() => {
                // للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('تم النسخ: ' + text, 'success');
            });
        }
        
        function fillDemoCredentials() {
            document.getElementById('workshop-code').value = 'DEMO-001';
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            showToast('تم ملء البيانات التجريبية', 'info');
        }
        
        function showPasswordRecovery() {
            const recoveryHTML = `
                <div class="password-recovery">
                    <h3><i class="fas fa-key"></i> استعادة كلمة المرور</h3>
                    <p>للحصول على كلمة مرور جديدة، يرجى التواصل مع الدعم الفني:</p>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>+213 555 123 456</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <span>السبت - الخميس (8:00 - 17:00)</span>
                        </div>
                    </div>
                    <div class="recovery-actions">
                        <button class="btn btn-secondary" onclick="hideModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `;
            showModal('استعادة كلمة المرور', recoveryHTML);
        }

        // دوال جديدة محسنة
        function showSystemInfo() {
            const systemHTML = `
                <div class="system-info">
                    <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">اسم النظام:</span>
                            <span class="value">نظام إدارة محطة الغاز</span>
                        </div>
                        <div class="info-item">
                            <span class="label">النسخة:</span>
                            <span class="value">2.0.0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">تاريخ الإصدار:</span>
                            <span class="value">ديسمبر 2024</span>
                        </div>
                        <div class="info-item">
                            <span class="label">نوع النسخة:</span>
                            <span class="value">نسخة العميل</span>
                        </div>
                        <div class="info-item">
                            <span class="label">المطور:</span>
                            <span class="value">فريق تطوير الأنظمة</span>
                        </div>
                        <div class="info-item">
                            <span class="label">الدعم:</span>
                            <span class="value">24/7</span>
                        </div>
                    </div>
                    <div class="features-list">
                        <h4>الميزات المتاحة:</h4>
                        <ul>
                            <li><i class="fas fa-check"></i> إدارة العملاء</li>
                            <li><i class="fas fa-check"></i> بطاقات الغاز</li>
                            <li><i class="fas fa-check"></i> جدولة المواعيد</li>
                            <li><i class="fas fa-check"></i> جدول الإرسال</li>
                            <li><i class="fas fa-check"></i> التقارير والإحصائيات</li>
                            <li><i class="fas fa-check"></i> النسخ الاحتياطية</li>
                        </ul>
                    </div>
                    <div class="system-actions">
                        <button class="btn btn-secondary" onclick="hideModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `;
            showModal('معلومات النظام', systemHTML);
        }

        function checkActivationStatus() {
            const statusHTML = `
                <div class="activation-status">
                    <h3><i class="fas fa-search"></i> فحص حالة التفعيل</h3>
                    <form id="status-check-form">
                        <div class="form-group">
                            <label for="request-number">رقم طلب التفعيل:</label>
                            <input type="text" id="request-number" placeholder="REQ-XXXXXXXXX" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                فحص الحالة
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="hideModal()">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                        </div>
                    </form>
                    <div id="status-result" style="display: none;">
                        <!-- نتيجة الفحص -->
                    </div>
                </div>
            `;
            showModal('فحص حالة التفعيل', statusHTML);

            document.getElementById('status-check-form').addEventListener('submit', function(e) {
                e.preventDefault();
                const requestNumber = document.getElementById('request-number').value.trim();
                if (requestNumber) {
                    checkRequestStatus(requestNumber);
                }
            });
        }

        function checkRequestStatus(requestNumber) {
            const savedRequests = JSON.parse(localStorage.getItem('activationRequests')) || [];
            const request = savedRequests.find(r => r.requestNumber === requestNumber);

            const resultDiv = document.getElementById('status-result');

            if (request) {
                resultDiv.innerHTML = `
                    <div class="status-found">
                        <h4><i class="fas fa-check-circle"></i> تم العثور على الطلب</h4>
                        <div class="request-details">
                            <div><strong>رقم الطلب:</strong> ${request.requestNumber}</div>
                            <div><strong>اسم الورشة:</strong> ${request.workshopName}</div>
                            <div><strong>الحالة:</strong> <span class="status-${request.status}">${getStatusText(request.status)}</span></div>
                            <div><strong>تاريخ الطلب:</strong> ${formatDateTime(request.timestamp)}</div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="status-not-found">
                        <h4><i class="fas fa-exclamation-triangle"></i> لم يتم العثور على الطلب</h4>
                        <p>تأكد من رقم الطلب أو تواصل مع الدعم الفني</p>
                    </div>
                `;
            }

            resultDiv.style.display = 'block';
        }

        function getStatusText(status) {
            const statusTexts = {
                'pending': 'قيد المراجعة',
                'approved': 'تم الموافقة',
                'rejected': 'مرفوض'
            };
            return statusTexts[status] || status;
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('ar-DZ');
        }
    </script>
</body>
</html>
