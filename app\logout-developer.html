<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل خروج المطور</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .logout-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .logout-icon {
            font-size: 4rem;
            color: #e74c3c;
            margin-bottom: 20px;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .message {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message {
            display: none;
            color: #27ae60;
            font-weight: bold;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-icon">
            <i class="fas fa-sign-out-alt"></i>
        </div>
        
        <h1>تسجيل خروج المطور</h1>
        
        <div class="message">
            هل أنت متأكد من تسجيل الخروج من لوحة تحكم المطور؟
            <br>
            سيتم إعادة توجيهك إلى صفحة تسجيل الدخول.
        </div>

        <div class="actions">
            <button class="btn btn-danger" onclick="confirmLogout()">
                <i class="fas fa-sign-out-alt"></i>
                تأكيد تسجيل الخروج
            </button>
            
            <button class="btn" onclick="cancelLogout()">
                <i class="fas fa-arrow-left"></i>
                إلغاء
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري تسجيل الخروج...</p>
        </div>

        <div class="success-message" id="success-message">
            <i class="fas fa-check-circle"></i>
            تم تسجيل الخروج بنجاح!
        </div>
    </div>

    <script>
        // تسجيل الخروج المؤكد
        function confirmLogout() {
            // إظهار رسالة التحميل
            document.querySelector('.actions').style.display = 'none';
            document.querySelector('.message').style.display = 'none';
            document.getElementById('loading').style.display = 'block';

            // محاكاة عملية تسجيل الخروج
            setTimeout(() => {
                // حذف جلسة المطور
                sessionStorage.removeItem('developerSession');
                localStorage.removeItem('developerSession');
                
                // حذف أي بيانات جلسة أخرى متعلقة بالمطور
                sessionStorage.removeItem('gasSystemSession');
                
                // إظهار رسالة النجاح
                document.getElementById('loading').style.display = 'none';
                document.getElementById('success-message').style.display = 'block';
                
                // إعادة التوجيه لصفحة تسجيل الدخول
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }, 1500);
        }

        // إلغاء تسجيل الخروج
        function cancelLogout() {
            window.location.href = 'developer.html';
        }

        // تسجيل خروج فوري (للاستخدام المباشر)
        function immediateLogout() {
            sessionStorage.removeItem('developerSession');
            localStorage.removeItem('developerSession');
            sessionStorage.removeItem('gasSystemSession');
            
            window.location.href = 'login.html';
        }

        // التحقق من وجود جلسة مطور
        document.addEventListener('DOMContentLoaded', function() {
            const developerSession = sessionStorage.getItem('developerSession');
            if (!developerSession) {
                // إذا لم تكن هناك جلسة مطور، قم بالتوجيه مباشرة
                document.querySelector('.message').innerHTML = 'لا توجد جلسة مطور نشطة. سيتم إعادة توجيهك لصفحة تسجيل الدخول.';
                document.querySelector('.actions').style.display = 'none';
                
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        });
    </script>
</body>
</html>
