<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة محطة الغاز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="icon" type="image/png" href="assets/future-fuel-icon (8).png">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo i {
            font-size: 4rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .logo h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .logo p {
            color: #7f8c8d;
            margin: 0;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .demo-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .demo-info h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .demo-info p {
            margin: 5px 0;
            font-size: 13px;
            color: #6c757d;
        }

        .quick-login {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }

        .quick-login:hover {
            background: #218838;
        }

        .links {
            margin-top: 20px;
        }

        .links a {
            display: block;
            color: #3498db;
            text-decoration: none;
            margin: 8px 0;
            font-size: 14px;
        }

        .links a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-gas-pump"></i>
            <h1>نظام إدارة محطة الغاز</h1>
            <p>تسجيل الدخول</p>
        </div>

        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <div class="demo-info">
            <h4><i class="fas fa-info-circle"></i> بيانات تجريبية</h4>
            <p><strong>المدير:</strong> admin / admin123</p>
            <p><strong>المستخدم:</strong> user / user123</p>
            <button class="quick-login" onclick="quickLogin()">
                <i class="fas fa-bolt"></i> دخول سريع
            </button>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
            </button>
        </form>

        <div class="links">
            <a href="#" onclick="requestActivation()">
                <i class="fas fa-key"></i>
                طلب تفعيل النظام
            </a>
            <a href="license-login.html">
                <i class="fas fa-shield-alt"></i>
                لوحة التحكم في التراخيص
            </a>
            <a href="#" onclick="showSupport()">
                <i class="fas fa-headset"></i>
                الدعم الفني
            </a>
        </div>
    </div>

    <script>
        // بيانات تسجيل الدخول
        const users = {
            admin: { password: 'admin123', name: 'مدير النظام', role: 'admin' },
            user: { password: 'user123', name: 'مستخدم عادي', role: 'user' }
        };

        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (users[username] && users[username].password === password) {
                // حفظ بيانات الجلسة
                const session = {
                    workshopId: 'main-system',
                    userId: username,
                    username: username,
                    name: users[username].name,
                    role: users[username].role,
                    loginTime: new Date().toISOString(),
                    isActive: true
                };
                
                sessionStorage.setItem('gasSystemSession', JSON.stringify(session));
                
                showMessage('تم تسجيل الدخول بنجاح!', 'success');
                
                setTimeout(() => {
                    window.location.href = 'main.html';
                }, 1000);
            } else {
                showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        });

        // دخول سريع
        function quickLogin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            
            setTimeout(() => {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }, 500);
        }

        // طلب تفعيل
        function requestActivation() {
            alert('لطلب تفعيل النظام، يرجى التواصل مع الدعم الفني:\n\nالهاتف: +213 555 123 456\nالبريد: <EMAIL>');
        }

        // الدعم الفني
        function showSupport() {
            alert('الدعم الفني:\n\nالهاتف: +213 555 123 456\nالبريد: <EMAIL>\nساعات العمل: السبت - الخميس (8:00 - 17:00)');
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const errorDiv = document.getElementById('error-message');
            const successDiv = document.getElementById('success-message');
            
            // إخفاء جميع الرسائل
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            // عرض الرسالة المناسبة
            if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            } else if (type === 'success') {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';
            }, 3000);
        }

        // التحقق من الجلسة الموجودة
        window.addEventListener('load', function() {
            const session = sessionStorage.getItem('gasSystemSession');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    if (sessionData.isActive) {
                        showMessage('تم العثور على جلسة نشطة، جاري إعادة التوجيه...', 'success');
                        setTimeout(() => {
                            window.location.href = 'main.html';
                        }, 1000);
                    }
                } catch (error) {
                    sessionStorage.removeItem('gasSystemSession');
                }
            }
        });
    </script>
</body>
</html>
