// ==================== نسخة العميل - نظام تسجيل الدخول ====================

// بيانات النظام (نسخة مبسطة للعميل)
let systemData = {
    workshops: JSON.parse(localStorage.getItem('systemWorkshops')) || [
        {
            id: 'demo-001',
            code: 'DEMO-001',
            name: 'الورشة التجريبية',
            owner: 'مدير النظام',
            licenseType: 'trial',
            expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            isActive: true,
            users: [
                {
                    id: 'user-001',
                    username: 'admin',
                    password: 'admin123',
                    role: 'admin',
                    isActive: true
                }
            ],
            createdAt: new Date().toISOString()
        }
    ]
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل نظام تسجيل الدخول - نسخة العميل');
    
    setupLoginForm();
    checkExistingSession();
    loadWorkshopsList();
    
    console.log('✅ تم تحميل النظام بنجاح');
});

// إعداد نموذج تسجيل الدخول
function setupLoginForm() {
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // إعداد زر إظهار/إخفاء كلمة المرور
    const togglePassword = document.querySelector('.toggle-password');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
}

// معالجة تسجيل الدخول
function handleLogin(e) {
    e.preventDefault();
    
    const workshopCode = document.getElementById('workshop-code').value.trim();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    if (!workshopCode || !username || !password) {
        showToast('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    // البحث عن الورشة
    const workshop = systemData.workshops.find(w => w.code === workshopCode);
    
    if (!workshop) {
        showToast('كود الورشة غير صحيح', 'error');
        return;
    }
    
    if (!workshop.isActive) {
        showToast('الورشة معطلة. يرجى التواصل مع المطور', 'error');
        return;
    }
    
    // التحقق من صلاحية الترخيص
    if (new Date(workshop.expiryDate) < new Date()) {
        showToast('انتهت صلاحية ترخيص الورشة. يرجى التواصل مع المطور', 'error');
        return;
    }
    
    // البحث عن المستخدم
    const user = workshop.users.find(u => u.username === username && u.password === password);
    
    if (!user) {
        showToast('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
        return;
    }
    
    if (!user.isActive) {
        showToast('المستخدم معطل', 'error');
        return;
    }
    
    // إنشاء جلسة
    const session = {
        workshopId: workshop.id,
        workshopCode: workshop.code,
        userId: user.id,
        username: user.username,
        role: user.role,
        loginTime: new Date().toISOString()
    };
    
    // حفظ الجلسة
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem('gasSystemSession', JSON.stringify(session));
    
    // تحديث آخر تسجيل دخول
    workshop.lastLogin = new Date().toISOString();
    saveSystemData();
    
    showToast('تم تسجيل الدخول بنجاح', 'success');
    
    // إعادة التوجيه للنظام الرئيسي
    setTimeout(() => {
        window.location.href = 'main.html';
    }, 1000);
}

// التحقق من وجود جلسة
function checkExistingSession() {
    const savedSession = localStorage.getItem('gasSystemSession') || sessionStorage.getItem('gasSystemSession');
    
    if (savedSession) {
        try {
            const session = JSON.parse(savedSession);
            const workshop = systemData.workshops.find(w => w.id === session.workshopId);
            
            if (workshop && workshop.isActive && new Date(workshop.expiryDate) > new Date()) {
                // جلسة صالحة - إعادة توجيه
                window.location.href = 'main.html';
                return;
            }
        } catch (error) {
            console.error('خطأ في قراءة الجلسة:', error);
        }
        
        // مسح الجلسة غير الصالحة
        localStorage.removeItem('gasSystemSession');
        sessionStorage.removeItem('gasSystemSession');
    }
}

// تحميل قائمة الورشات
function loadWorkshopsList() {
    // يمكن إضافة منطق لعرض قائمة الورشات المتاحة
    console.log(`تم تحميل ${systemData.workshops.length} ورشة`);
}

// طلب تفعيل جديد
function requestActivation() {
    const modalBody = `
        <div class="activation-request-form">
            <h3>طلب تفعيل جديد</h3>
            <p>املأ النموذج أدناه لطلب تفعيل النظام من المطور</p>
            
            <form id="activation-request-form">
                <div class="form-group">
                    <label for="workshop-name">اسم الورشة: *</label>
                    <input type="text" id="workshop-name" required placeholder="أدخل اسم الورشة">
                </div>
                
                <div class="form-group">
                    <label for="owner-name">اسم المالك: *</label>
                    <input type="text" id="owner-name" required placeholder="أدخل اسم مالك الورشة">
                </div>
                
                <div class="form-group">
                    <label for="phone-number">رقم الهاتف: *</label>
                    <input type="tel" id="phone-number" required placeholder="**********">
                </div>
                
                <div class="form-group">
                    <label for="email-address">البريد الإلكتروني:</label>
                    <input type="email" id="email-address" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="license-type-request">نوع الترخيص المطلوب:</label>
                    <select id="license-type-request">
                        <option value="trial">تجريبي (30 يوم) - مجاني</option>
                        <option value="professional">احترافي (سنة واحدة)</option>
                        <option value="enterprise">مؤسسي (بدون انتهاء)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="activation-code-input">كود التفعيل (إذا كان متوفراً):</label>
                    <input type="text" id="activation-code-input" placeholder="ACT-XXXX-XXXX-XXXX">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        إرسال طلب التفعيل
                    </button>
                    <button type="button" class="btn btn-info" onclick="tryActivationCode()">
                        <i class="fas fa-key"></i>
                        تجربة كود التفعيل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;
    
    showModal('طلب تفعيل النظام', modalBody);
    
    document.getElementById('activation-request-form').addEventListener('submit', handleActivationRequest);
}

// معالجة طلب التفعيل
function handleActivationRequest(e) {
    e.preventDefault();
    
    const requestData = {
        workshopName: document.getElementById('workshop-name').value.trim(),
        ownerName: document.getElementById('owner-name').value.trim(),
        phoneNumber: document.getElementById('phone-number').value.trim(),
        emailAddress: document.getElementById('email-address').value.trim(),
        licenseType: document.getElementById('license-type-request').value,
        activationCode: document.getElementById('activation-code-input').value.trim()
    };
    
    if (!requestData.workshopName || !requestData.ownerName || !requestData.phoneNumber) {
        showToast('يرجى ملء الحقول المطلوبة', 'error');
        return;
    }
    
    // محاكاة إرسال الطلب
    showToast('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً', 'success');
    hideModal();
    
    // عرض معلومات المتابعة
    showContactInfo();
}

// تجربة كود التفعيل
function tryActivationCode() {
    const activationCode = document.getElementById('activation-code-input').value.trim();
    
    if (!activationCode) {
        showToast('يرجى إدخال كود التفعيل', 'error');
        return;
    }
    
    // محاكاة التحقق من كود التفعيل
    const validCodes = ['ACT-TRIAL-123456', 'ACT-PROFESSIONAL-789012', 'ACT-ENTERPRISE-345678'];
    
    if (validCodes.includes(activationCode) || activationCode.startsWith('ACT-')) {
        const licenseType = activationCode.includes('TRIAL') ? 'trial' : 
                          activationCode.includes('PROFESSIONAL') ? 'professional' : 'enterprise';
        
        const workshopName = document.getElementById('workshop-name').value.trim() || 'ورشة جديدة';
        const ownerName = document.getElementById('owner-name').value.trim() || 'مالك الورشة';
        
        createWorkshopFromActivation(activationCode, licenseType, workshopName, ownerName);
        hideModal();
        showToast('تم تفعيل النظام بنجاح!', 'success');
    } else {
        showToast('كود التفعيل غير صالح', 'error');
    }
}

// إنشاء ورشة من كود التفعيل
function createWorkshopFromActivation(activationCode, licenseType, workshopName, ownerName) {
    const workshop = {
        id: generateId(),
        code: generateWorkshopCode(),
        name: workshopName,
        owner: ownerName,
        licenseType: licenseType,
        activationCode: activationCode,
        expiryDate: calculateExpiryDate(licenseType),
        isActive: true,
        users: [
            {
                id: generateId(),
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                isActive: true
            }
        ],
        createdAt: new Date().toISOString()
    };
    
    systemData.workshops.push(workshop);
    saveSystemData();
    
    // عرض معلومات الورشة الجديدة
    showWorkshopInfo(workshop);
}

// عرض معلومات الاتصال
function showContactInfo() {
    const contactHTML = `
        <div class="contact-info">
            <h3><i class="fas fa-phone"></i> معلومات التواصل</h3>
            <div class="contact-details">
                <div><strong>الهاتف:</strong> +213 555 123 456</div>
                <div><strong>البريد الإلكتروني:</strong> <EMAIL></div>
                <div><strong>ساعات العمل:</strong> السبت - الخميس (8:00 - 17:00)</div>
            </div>
            <p>سيتم التواصل معك خلال 24 ساعة لتفعيل النظام</p>
            <button class="btn btn-secondary" onclick="hideModal()">إغلاق</button>
        </div>
    `;
    
    showModal('معلومات التواصل', contactHTML);
}

// عرض الدعم الفني
function showSupport() {
    showContactInfo();
}

// دوال مساعدة
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateWorkshopCode() {
    const prefix = 'WS';
    const year = new Date().getFullYear().toString().slice(-2);
    const sequence = Math.floor(Math.random() * 999).toString().padStart(3, '0');
    return `${prefix}-${year}-${sequence}`;
}

function calculateExpiryDate(licenseType) {
    const expiryDate = new Date();
    switch (licenseType) {
        case 'trial': expiryDate.setDate(expiryDate.getDate() + 30); break;
        case 'professional': expiryDate.setFullYear(expiryDate.getFullYear() + 1); break;
        case 'enterprise': expiryDate.setFullYear(expiryDate.getFullYear() + 10); break;
    }
    return expiryDate.toISOString();
}

function saveSystemData() {
    localStorage.setItem('systemWorkshops', JSON.stringify(systemData.workshops));
}

function showWorkshopInfo(workshop) {
    const workshopInfoHTML = `
        <div class="workshop-info-display">
            <h3><i class="fas fa-check-circle text-success"></i> تم تفعيل النظام بنجاح!</h3>
            <div class="workshop-details">
                <div><strong>كود الورشة:</strong> ${workshop.code}</div>
                <div><strong>اسم المستخدم:</strong> admin</div>
                <div><strong>كلمة المرور:</strong> admin123</div>
            </div>
            <button class="btn btn-success" onclick="loginWithNewWorkshop('${workshop.code}')">
                تسجيل الدخول الآن
            </button>
        </div>
    `;
    
    showModal('تفعيل ناجح', workshopInfoHTML);
}

function loginWithNewWorkshop(workshopCode) {
    document.getElementById('workshop-code').value = workshopCode;
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin123';
    hideModal();
}

function showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">${content}</div>
        </div>
    `;
    document.body.appendChild(modal);
}

function hideModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => modal.remove());
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

console.log('✅ تم تحميل نظام تسجيل الدخول - نسخة العميل');
