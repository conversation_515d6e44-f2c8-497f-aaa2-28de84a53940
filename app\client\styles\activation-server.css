/* ==================== متغيرات CSS ==================== */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --bg-color: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1e293b;
    --text-color: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ==================== إعدادات عامة ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
}

/* ==================== خادم التفعيل ==================== */
.activation-server {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ==================== الشريط العلوي ==================== */
.server-header {
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.server-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.server-status.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.server-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.server-ip {
    color: var(--primary-color);
    font-weight: bold;
}

.port {
    color: var(--text-secondary);
}

/* ==================== المحتوى الرئيسي ==================== */
.server-main {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* ==================== لوحة التحكم ==================== */
.control-panel {
    background: var(--bg-color);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.panel-section {
    margin-bottom: 2rem;
}

.panel-section:last-child {
    margin-bottom: 0;
}

.panel-section h2 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-size: 1.3rem;
}

.panel-section h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    font-size: 1.1rem;
}

/* ==================== إحصائيات سريعة ==================== */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0 0 0.25rem 0;
    color: var(--text-color);
}

.stat-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ==================== مولد التراخيص السريع ==================== */
.quick-generator {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.generator-form .form-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.generator-form select,
.generator-form input {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-color);
    color: var(--text-color);
    flex: 1;
    min-width: 150px;
}

.generated-code {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.code-display label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.code-value {
    display: flex;
    gap: 0.5rem;
}

.code-value input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-color);
    font-family: monospace;
    font-size: 0.9rem;
}

/* ==================== الأقسام ==================== */
.activation-requests,
.connected-clients,
.activity-log {
    background: var(--bg-color);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-color);
    font-size: 1.3rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.client-count {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* ==================== الجداول ==================== */
.requests-table {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-color);
    border-radius: 8px;
    overflow: hidden;
}

.data-table th {
    background: var(--bg-secondary);
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.data-table tr:hover {
    background: var(--bg-secondary);
}

/* ==================== شبكة العملاء ==================== */
.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.client-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.client-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.client-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.client-name {
    font-weight: bold;
    color: var(--text-color);
}

.client-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.client-status.connected {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.client-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* ==================== سجل الأنشطة ==================== */
.log-container {
    max-height: 400px;
    overflow-y: auto;
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1rem;
}

.log-entry {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    font-family: monospace;
    color: var(--text-secondary);
    font-size: 0.8rem;
    min-width: 80px;
}

.log-message {
    flex: 1;
    color: var(--text-color);
    font-size: 0.9rem;
}

.log-type {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.log-type.info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.log-type.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.log-type.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.log-type.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* ==================== الأزرار ==================== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #0891b2;
}

.btn-secondary {
    background: var(--text-secondary);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* ==================== النوافذ المنبثقة ==================== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-color);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: var(--bg-secondary);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    border-radius: 16px 16px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

.modal-body {
    padding: 2rem;
}

/* ==================== نموذج التفعيل ==================== */
.activation-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.client-info {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.license-options .form-group {
    margin-bottom: 1rem;
}

.license-options label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.license-options input,
.license-options select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-color);
    color: var(--text-color);
}

.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.features-list label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: normal;
}

.features-list label:hover {
    background: var(--border-color);
}

.activation-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* ==================== رسائل التنبيه ==================== */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
}

.toast {
    background: var(--bg-color);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ==================== تنسيقات الشاشات الصغيرة ==================== */
@media (max-width: 768px) {
    .server-main {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .generator-form .form-row {
        flex-direction: column;
    }
    
    .clients-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .activation-actions {
        flex-direction: column;
    }
}

/* ==================== حالات خاصة ==================== */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.approved {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.rejected {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.ip-address {
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.workshop-code {
    font-family: monospace;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.license-key {
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    word-break: break-all;
    border: 1px solid var(--border-color);
}
