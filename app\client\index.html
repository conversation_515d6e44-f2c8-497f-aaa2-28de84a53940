<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة محطة الغاز - تسجيل الدخول</title>
    <link rel="stylesheet" href="styles/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <!-- الخلفية المتحركة -->
        <div class="animated-background">
            <div class="floating-shape shape-1"></div>
            <div class="floating-shape shape-2"></div>
            <div class="floating-shape shape-3"></div>
            <div class="floating-shape shape-4"></div>
        </div>

        <!-- بطاقة تسجيل الدخول -->
        <div class="login-card">
            <!-- الشعار والعنوان -->
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1>نظام إدارة محطة الغاز</h1>
                <p>إدارة شاملة وموثوقة لورشة الغاز</p>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="workshop-code">
                        <i class="fas fa-building"></i>
                        كود الورشة
                    </label>
                    <input 
                        type="text" 
                        id="workshop-code" 
                        name="workshop-code" 
                        required 
                        placeholder="أدخل كود الورشة"
                        autocomplete="organization"
                    >
                </div>

                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        required 
                        placeholder="أدخل اسم المستخدم"
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required 
                            placeholder="أدخل كلمة المرور"
                            autocomplete="current-password"
                        >
                        <button type="button" class="toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="remember-me" name="remember-me">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" onclick="showPasswordRecovery()" class="forgot-password">
                        نسيت كلمة المرور؟
                    </a>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- روابط إضافية -->
            <div class="login-footer">
                <div class="divider">
                    <span>أو</span>
                </div>

                <div class="support-links">
                    <a href="#" onclick="requestActivation()">
                        <i class="fas fa-key"></i>
                        طلب تفعيل النظام
                    </a>
                    <a href="#" onclick="showSupport()">
                        <i class="fas fa-headset"></i>
                        الدعم الفني
                    </a>
                </div>

                <!-- معلومات النسخة -->
                <div class="version-info">
                    <p>نسخة العميل v2.0</p>
                    <p>&copy; 2024 نظام إدارة محطة الغاز</p>
                </div>
            </div>
        </div>

        <!-- معلومات تجريبية -->
        <div class="demo-info">
            <div class="demo-card">
                <h3><i class="fas fa-info-circle"></i> للتجربة السريعة</h3>
                <div class="demo-credentials">
                    <div class="credential-item">
                        <span class="label">كود الورشة:</span>
                        <span class="value">DEMO-001</span>
                        <button class="copy-btn" onclick="copyToClipboard('DEMO-001')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="credential-item">
                        <span class="label">المستخدم:</span>
                        <span class="value">admin</span>
                        <button class="copy-btn" onclick="copyToClipboard('admin')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="credential-item">
                        <span class="label">كلمة المرور:</span>
                        <span class="value">admin123</span>
                        <button class="copy-btn" onclick="copyToClipboard('admin123')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <button class="demo-login-btn" onclick="fillDemoCredentials()">
                    <i class="fas fa-play"></i>
                    تجربة النظام الآن
                </button>
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div id="toast-container" class="toast-container"></div>

    <!-- JavaScript -->
    <script src="scripts/login.js"></script>
    
    <script>
        // دوال إضافية للواجهة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('تم النسخ: ' + text, 'success');
            }).catch(() => {
                // للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('تم النسخ: ' + text, 'success');
            });
        }
        
        function fillDemoCredentials() {
            document.getElementById('workshop-code').value = 'DEMO-001';
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            showToast('تم ملء البيانات التجريبية', 'info');
        }
        
        function showPasswordRecovery() {
            const recoveryHTML = `
                <div class="password-recovery">
                    <h3><i class="fas fa-key"></i> استعادة كلمة المرور</h3>
                    <p>للحصول على كلمة مرور جديدة، يرجى التواصل مع الدعم الفني:</p>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>+213 555 123 456</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <span>السبت - الخميس (8:00 - 17:00)</span>
                        </div>
                    </div>
                    <div class="recovery-actions">
                        <button class="btn btn-secondary" onclick="hideModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `;
            showModal('استعادة كلمة المرور', recoveryHTML);
        }
    </script>
</body>
</html>
