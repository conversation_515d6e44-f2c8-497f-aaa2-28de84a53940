<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم في التراخيص - نظام إدارة محطة الغاز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="icon" type="image/png" href="assets/future-fuel-icon (8).png">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #22c55e;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --bg-color: #ffffff;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--primary-color);
        }

        .logo i {
            font-size: 2rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--light-color);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            border: 1px solid var(--border-color);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* Main Container */
        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* Dashboard Cards */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .dashboard-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }

        .card-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-color);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-icon.primary { background: var(--primary-color); }
        .card-icon.success { background: var(--success-color); }
        .card-icon.warning { background: var(--warning-color); }
        .card-icon.danger { background: var(--danger-color); }
        .card-icon.info { background: var(--info-color); }

        .card-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .card-description {
            color: var(--text-color);
            opacity: 0.7;
            font-size: 0.9rem;
        }

        /* Action Buttons */
        .action-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: var(--light-color);
            border-radius: 12px;
            padding: 1.5rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            border-color: var(--primary-color);
            background: white;
            transform: translateY(-2px);
        }

        .action-card h3 {
            color: var(--text-color);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .action-card p {
            color: var(--text-color);
            opacity: 0.7;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #16a34a;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-secondary {
            background: var(--light-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        /* Quick Access */
        .quick-access {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            z-index: 50;
        }

        .quick-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-btn:hover {
            transform: scale(1.1);
            background: #1d4ed8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-container {
                padding: 0 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                <h1>لوحة التحكم في التراخيص</h1>
            </div>
            <div class="header-actions">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span id="current-user">مدير النظام</span>
                </div>
                <button class="btn btn-secondary" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Dashboard Cards -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        التراخيص النشطة
                    </div>
                </div>
                <div class="card-value" id="active-licenses">0</div>
                <div class="card-description">ترخيص نشط حالياً</div>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        طلبات معلقة
                    </div>
                </div>
                <div class="card-value" id="pending-requests">0</div>
                <div class="card-description">طلب في انتظار المراجعة</div>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        تراخيص منتهية
                    </div>
                </div>
                <div class="card-value" id="expired-licenses">0</div>
                <div class="card-description">ترخيص منتهي الصلاحية</div>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon info">
                            <i class="fas fa-building"></i>
                        </div>
                        إجمالي الورشات
                    </div>
                </div>
                <div class="card-value" id="total-workshops">0</div>
                <div class="card-description">ورشة مسجلة</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="action-section">
            <h2 class="section-title">
                <i class="fas fa-bolt"></i>
                الإجراءات السريعة
            </h2>
            <div class="action-grid">
                <div class="action-card" onclick="showCreateLicense()">
                    <h3><i class="fas fa-plus-circle"></i> إنشاء ترخيص جديد</h3>
                    <p>إنشاء ترخيص جديد لورشة مع تحديد نوع الترخيص ومدة الصلاحية</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إنشاء ترخيص
                    </button>
                </div>

                <div class="action-card" onclick="showPendingRequests()">
                    <h3><i class="fas fa-list-alt"></i> مراجعة الطلبات</h3>
                    <p>مراجعة والموافقة على طلبات التفعيل الواردة من العملاء</p>
                    <button class="btn btn-warning">
                        <i class="fas fa-eye"></i>
                        مراجعة الطلبات
                    </button>
                </div>

                <div class="action-card" onclick="showLicenseManager()">
                    <h3><i class="fas fa-cogs"></i> إدارة التراخيص</h3>
                    <p>عرض وتعديل وحذف التراخيص الموجودة مع إمكانية التجديد</p>
                    <button class="btn btn-success">
                        <i class="fas fa-list"></i>
                        إدارة التراخيص
                    </button>
                </div>

                <div class="action-card" onclick="showWorkshopManager()">
                    <h3><i class="fas fa-building"></i> إدارة الورشات</h3>
                    <p>إدارة معلومات الورشات المسجلة وحالة تراخيصها</p>
                    <button class="btn btn-info">
                        <i class="fas fa-building"></i>
                        إدارة الورشات
                    </button>
                </div>

                <div class="action-card" onclick="showReports()">
                    <h3><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h3>
                    <p>عرض تقارير مفصلة عن التراخيص والورشات والإحصائيات</p>
                    <button class="btn btn-secondary">
                        <i class="fas fa-chart-line"></i>
                        عرض التقارير
                    </button>
                </div>

                <div class="action-card" onclick="showSettings()">
                    <h3><i class="fas fa-cog"></i> إعدادات النظام</h3>
                    <p>تكوين إعدادات النظام وأنواع التراخيص والصلاحيات</p>
                    <button class="btn btn-secondary">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Buttons -->
    <div class="quick-access">
        <button class="quick-btn" onclick="showCreateLicense()" title="إنشاء ترخيص سريع">
            <i class="fas fa-plus"></i>
        </button>
        <button class="quick-btn" onclick="showPendingRequests()" title="الطلبات المعلقة">
            <i class="fas fa-bell"></i>
        </button>
        <button class="quick-btn" onclick="showHelp()" title="المساعدة">
            <i class="fas fa-question"></i>
        </button>
    </div>

    <script src="scripts/license-control-panel.js"></script>
</body>
</html>
