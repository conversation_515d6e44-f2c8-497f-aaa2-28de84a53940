@echo off
chcp 65001 >nul
title نظام إدارة محطة الغاز - تشغيل سريع

echo.
echo ========================================
echo    نظام إدارة محطة الغاز
echo    نسخة العميل v2.0
echo ========================================
echo.

echo 🚀 جاري تشغيل النظام...
echo.

REM البحث عن المتصفحات المتاحة
set "browser_found=false"

REM محاولة فتح Chrome
if exist "%ProgramFiles%\Google\Chrome\Application\chrome.exe" (
    echo ✅ تم العثور على Google Chrome
    start "" "%ProgramFiles%\Google\Chrome\Application\chrome.exe" "%~dp0index.html"
    set "browser_found=true"
    goto :success
)

if exist "%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe" (
    echo ✅ تم العثور على Google Chrome
    start "" "%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe" "%~dp0index.html"
    set "browser_found=true"
    goto :success
)

REM محاولة فتح Firefox
if exist "%ProgramFiles%\Mozilla Firefox\firefox.exe" (
    echo ✅ تم العثور على Mozilla Firefox
    start "" "%ProgramFiles%\Mozilla Firefox\firefox.exe" "%~dp0index.html"
    set "browser_found=true"
    goto :success
)

if exist "%ProgramFiles(x86)%\Mozilla Firefox\firefox.exe" (
    echo ✅ تم العثور على Mozilla Firefox
    start "" "%ProgramFiles(x86)%\Mozilla Firefox\firefox.exe" "%~dp0index.html"
    set "browser_found=true"
    goto :success
)

REM محاولة فتح Edge
if exist "%ProgramFiles(x86)%\Microsoft\Edge\Application\msedge.exe" (
    echo ✅ تم العثور على Microsoft Edge
    start "" "%ProgramFiles(x86)%\Microsoft\Edge\Application\msedge.exe" "%~dp0index.html"
    set "browser_found=true"
    goto :success
)

REM استخدام المتصفح الافتراضي
if "%browser_found%"=="false" (
    echo ✅ استخدام المتصفح الافتراضي
    start "" "%~dp0index.html"
)

:success
echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 📋 بيانات التجربة:
echo    كود الورشة: DEMO-001
echo    المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 📞 الدعم الفني: +213 555 123 456
echo 📧 البريد: <EMAIL>
echo ========================================
echo.

echo 💡 نصائح:
echo    - استخدم البيانات التجريبية للتعلم
echo    - اطلب تفعيل للحصول على ورشة خاصة
echo    - تواصل معنا للدعم الفني
echo.

pause
exit
