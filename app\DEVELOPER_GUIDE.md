# 🚀 دليل المطور - نظام إدارة محطة الغاز

## 📋 نظرة عامة

هذا النظام مصمم للتوزيع على ورشات الغاز مع نظام تراخيص مركزي يتيح لك التحكم الكامل في جميع النسخ الموزعة.

## 🎯 المكونات الرئيسية

### 1. نظام تسجيل الدخول (`login.html`)
- واجهة تسجيل دخول احترافية
- نظام ورشات مع أكواد فريدة
- طلب تفعيل عن بُعد
- استعادة كلمة المرور

### 2. لوحة تحكم المطور (`developer.html`)
- إدارة شاملة للورشات
- مولد التراخيص
- إحصائيات مفصلة
- النسخ الاحتياطية

### 3. خاد<PERSON> التفعيل المركزي (`activation-server.html`)
- تفعيل عن بُعد من جهازك
- مراقبة العملاء المتصلين
- إدارة طلبات التفعيل
- سجل الأنشطة

### 4. النظام الرئيسي (`main.html`)
- التحقق من التراخيص
- قيود الميزات حسب نوع الترخيص
- عرض معلومات الورشة

## 🔐 بيانات الدخول الافتراضية

### لوحة المطور:
- **مفتاح المطور:** `DEV-2024-GASMANAGEMENT-MASTER`
- **كلمة المرور:** `GasSystem@2024!`

### الورشة التجريبية:
- **كود الورشة:** `DEMO-001`
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 🎮 كيفية الاستخدام

### 1. إعداد النظام للتوزيع

#### أ. نسخ الملفات:
```
نسخ مجلد app/ كاملاً إلى جهاز العميل
```

#### ب. تشغيل خادم التفعيل (على جهازك):
1. افتح `activation-server.html`
2. سيعمل الخادم على `192.168.1.100:8080`
3. راقب طلبات التفعيل الواردة

### 2. إنشاء تراخيص جديدة

#### الطريقة الأولى - من لوحة المطور:
1. افتح `developer.html`
2. اذهب لقسم "إدارة التراخيص"
3. املأ بيانات الورشة
4. اختر نوع الترخيص
5. انسخ كود التفعيل وأرسله للعميل

#### الطريقة الثانية - من خادم التفعيل:
1. افتح `activation-server.html`
2. استخدم "مولد التراخيص السريع"
3. أدخل اسم الورشة ونوع الترخيص
4. انسخ كود التفعيل

### 3. تفعيل النظام عند العميل

#### الطريقة الأولى - كود التفعيل:
1. العميل يفتح `login.html`
2. يضغط "طلب تفعيل"
3. يدخل كود التفعيل الذي أرسلته
4. يضغط "تجربة كود التفعيل"

#### الطريقة الثانية - طلب عن بُعد:
1. العميل يملأ نموذج طلب التفعيل
2. تستلم الطلب في خادم التفعيل
3. توافق على الطلب وتحدد نوع الترخيص
4. يتم إرسال كود التفعيل للعميل

## 📊 أنواع التراخيص

### 1. تجريبي (Trial)
- **المدة:** 30 يوم
- **الميزات:** العملاء، البطاقات، المواعيد
- **السعر:** مجاني

### 2. احترافي (Professional)
- **المدة:** سنة واحدة
- **الميزات:** جميع ميزات التجريبي + جدول الإرسال، المخزون، التقارير
- **السعر:** حسب اتفاقك

### 3. مؤسسي (Enterprise)
- **المدة:** 10 سنوات
- **الميزات:** جميع الميزات + الموظفين، الرواتب، الصيانة
- **السعر:** حسب اتفاقك

## 🛠️ إدارة الورشات

### إضافة ورشة جديدة:
1. افتح لوحة المطور
2. اذهب لـ "إدارة الورشات"
3. اضغط "إضافة ورشة جديدة"
4. املأ البيانات واختر نوع الترخيص

### تمديد ترخيص:
1. في جدول الورشات، اضغط زر "تمديد"
2. حدد المدة الجديدة ونوع الترخيص
3. سيتم توليد مفتاح ترخيص جديد

### حذف ورشة:
1. اضغط زر "حذف" في جدول الورشات
2. تأكد من الحذف (لا يمكن التراجع)

## 📈 مراقبة النظام

### الإحصائيات المتاحة:
- عدد الورشات النشطة
- التراخيص المنتهية
- توزيع أنواع التراخيص
- العملاء المتصلين

### سجل الأنشطة:
- تسجيل دخول الورشات
- طلبات التفعيل
- تمديد التراخيص
- الأخطاء والتحذيرات

## 💾 النسخ الاحتياطية

### إنشاء نسخة احتياطية:
1. في لوحة المطور، اذهب لـ "النسخ الاحتياطية"
2. اضغط "إنشاء نسخة احتياطية"
3. سيتم تحميل ملف JSON

### استيراد نسخة احتياطية:
1. اضغط "استيراد نسخة احتياطية"
2. اختر ملف JSON
3. تأكد من الاستيراد

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. لا يمكن تسجيل الدخول:
- تأكد من صحة كود الورشة
- تحقق من صلاحية الترخيص
- تأكد من تفعيل الورشة

#### 2. انتهاء صلاحية الترخيص:
- مدد الترخيص من لوحة المطور
- أو أنشئ ترخيص جديد

#### 3. مشاكل في التفعيل:
- تأكد من صحة كود التفعيل
- تحقق من اتصال الشبكة
- راجع سجل الأنشطة

## 🌐 التوزيع على الشبكة

### إعداد خادم التفعيل:
1. شغل `activation-server.html` على جهازك
2. تأكد من أن العملاء يمكنهم الوصول لعنوان IP الخاص بك
3. راقب طلبات التفعيل الواردة

### أمان الشبكة:
- استخدم شبكة آمنة
- غير كلمات المرور الافتراضية
- راقب محاولات الدخول المشبوهة

## 📞 الدعم الفني

### معلومات الاتصال:
- **الهاتف:** +213 555 123 456
- **البريد:** <EMAIL>
- **ساعات العمل:** السبت - الخميس (8:00 - 17:00)

### تقديم الدعم:
1. راجع سجل الأخطاء
2. تحقق من حالة الترخيص
3. استخدم النسخ الاحتياطية عند الحاجة

## 🚀 تطوير النظام

### إضافة ميزات جديدة:
1. حدث ملفات JavaScript
2. أضف الميزة لقائمة الصلاحيات
3. اختبر مع أنواع التراخيص المختلفة

### تحديث التراخيص:
1. حدث `serverConfig.licenseTypes`
2. أضف الميزات الجديدة للقوائم
3. اختبر التوافق مع النسخ القديمة

## 📋 قائمة المراجعة

### قبل التوزيع:
- [ ] اختبار جميع أنواع التراخيص
- [ ] التأكد من عمل النسخ الاحتياطية
- [ ] اختبار طلبات التفعيل
- [ ] مراجعة معلومات الاتصال
- [ ] تحديث كلمات المرور

### بعد التوزيع:
- [ ] مراقبة طلبات التفعيل
- [ ] متابعة انتهاء التراخيص
- [ ] إنشاء نسخ احتياطية دورية
- [ ] تقديم الدعم الفني

---

**ملاحظة:** هذا النظام مصمم ليكون مرناً وقابلاً للتطوير. يمكنك تخصيصه حسب احتياجاتك وإضافة ميزات جديدة بسهولة.
