<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - نظام إدارة محطة الغاز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .test-header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ecf0f1;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h3 {
            color: #3498db;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .test-button.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .credentials {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .credential-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-indicator.green {
            background: #27ae60;
        }

        .status-indicator.red {
            background: #e74c3c;
        }

        .status-indicator.yellow {
            background: #f39c12;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-gas-pump"></i> اختبار سريع للنظام</h1>
            <p>اختبار جميع مكونات نظام إدارة محطة الغاز</p>
        </div>

        <!-- اختبار تسجيل الدخول -->
        <div class="test-section">
            <h3><i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول</h3>
            <div class="credentials">
                <div class="credential-item">
                    <span>المستخدم الإداري:</span>
                    <span>admin / admin123</span>
                </div>
                <div class="credential-item">
                    <span>المستخدم العادي:</span>
                    <span>user / user123</span>
                </div>
            </div>
            <a href="index.html" class="test-button">
                <i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول
            </a>
            <button class="test-button success" onclick="testAutoLogin()">
                <i class="fas fa-magic"></i> تسجيل دخول تلقائي
            </button>
        </div>

        <!-- اختبار النظام الرئيسي -->
        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt"></i> اختبار النظام الرئيسي</h3>
            <p>اختبار الوصول المباشر للنظام الرئيسي</p>
            <a href="main.html" class="test-button">
                <i class="fas fa-external-link-alt"></i> فتح النظام الرئيسي
            </a>
            <button class="test-button warning" onclick="checkSystemStatus()">
                <i class="fas fa-heartbeat"></i> فحص حالة النظام
            </button>
        </div>

        <!-- اختبار الملفات -->
        <div class="test-section">
            <h3><i class="fas fa-file-code"></i> اختبار الملفات</h3>
            <div id="file-status">
                <p>جاري فحص الملفات...</p>
            </div>
            <button class="test-button" onclick="checkFiles()">
                <i class="fas fa-search"></i> فحص الملفات
            </button>
        </div>

        <!-- اختبار البيانات -->
        <div class="test-section">
            <h3><i class="fas fa-database"></i> اختبار البيانات</h3>
            <div id="data-status">
                <p>جاري فحص البيانات...</p>
            </div>
            <button class="test-button" onclick="checkData()">
                <i class="fas fa-check"></i> فحص البيانات
            </button>
            <button class="test-button success" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            <button class="test-button danger" onclick="clearData()">
                <i class="fas fa-trash"></i> مسح البيانات
            </button>
        </div>

        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h3>
            <div id="test-results">
                <p>لم يتم تشغيل أي اختبارات بعد</p>
            </div>
        </div>
    </div>

    <script>
        // تسجيل دخول تلقائي
        function testAutoLogin() {
            // إنشاء جلسة تجريبية
            const session = {
                workshopId: 'demo-001',
                userId: 'admin',
                username: 'admin',
                role: 'admin',
                loginTime: new Date().toISOString(),
                isActive: true
            };
            
            sessionStorage.setItem('gasSystemSession', JSON.stringify(session));
            
            // إعادة التوجيه للنظام الرئيسي
            window.location.href = 'main.html';
        }

        // فحص حالة النظام
        function checkSystemStatus() {
            const results = [];
            
            // فحص sessionStorage
            const session = sessionStorage.getItem('gasSystemSession');
            results.push({
                test: 'جلسة المستخدم',
                status: session ? 'موجودة' : 'غير موجودة',
                success: !!session
            });
            
            // فحص localStorage
            const data = localStorage.getItem('gasShopData');
            results.push({
                test: 'بيانات النظام',
                status: data ? 'موجودة' : 'غير موجودة',
                success: !!data
            });
            
            displayResults(results);
        }

        // فحص الملفات
        function checkFiles() {
            const files = [
                'main.html',
                'scripts/main.js',
                'styles/main.css'
            ];
            
            const results = [];
            let completed = 0;
            
            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        results.push({
                            test: `ملف ${file}`,
                            status: response.ok ? 'موجود' : 'مفقود',
                            success: response.ok
                        });
                    })
                    .catch(() => {
                        results.push({
                            test: `ملف ${file}`,
                            status: 'خطأ في التحميل',
                            success: false
                        });
                    })
                    .finally(() => {
                        completed++;
                        if (completed === files.length) {
                            displayFileResults(results);
                        }
                    });
            });
        }

        // فحص البيانات
        function checkData() {
            const data = localStorage.getItem('gasShopData');
            const results = [];
            
            if (data) {
                try {
                    const parsedData = JSON.parse(data);
                    results.push({
                        test: 'بيانات العملاء',
                        status: `${parsedData.customers?.length || 0} عميل`,
                        success: true
                    });
                    results.push({
                        test: 'بطاقات الغاز',
                        status: `${parsedData.gasCards?.length || 0} بطاقة`,
                        success: true
                    });
                    results.push({
                        test: 'المواعيد',
                        status: `${parsedData.appointments?.length || 0} موعد`,
                        success: true
                    });
                } catch (error) {
                    results.push({
                        test: 'تحليل البيانات',
                        status: 'خطأ في التحليل',
                        success: false
                    });
                }
            } else {
                results.push({
                    test: 'البيانات',
                    status: 'لا توجد بيانات',
                    success: false
                });
            }
            
            displayDataResults(results);
        }

        // إنشاء بيانات تجريبية
        function createTestData() {
            const testData = {
                customers: [
                    {
                        id: 'test-001',
                        name: 'أحمد محمد',
                        phone: '0555123456',
                        address: 'الجزائر العاصمة',
                        email: '<EMAIL>'
                    }
                ],
                gasCards: [
                    {
                        id: 'card-001',
                        customerName: 'أحمد محمد',
                        vehicleNumber: '123456-16',
                        cardNumber: 'GAS-001',
                        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                    }
                ],
                appointments: [
                    {
                        id: 'app-001',
                        date: new Date().toISOString().split('T')[0],
                        time: '10:00',
                        customerName: 'أحمد محمد',
                        serviceType: 'تركيب'
                    }
                ]
            };
            
            localStorage.setItem('gasShopData', JSON.stringify(testData));
            alert('تم إنشاء البيانات التجريبية بنجاح!');
            checkData();
        }

        // مسح البيانات
        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('gasShopData');
                sessionStorage.removeItem('gasSystemSession');
                alert('تم مسح البيانات بنجاح!');
                checkData();
            }
        }

        // عرض النتائج
        function displayResults(results) {
            const container = document.getElementById('test-results');
            let html = '<h4>نتائج فحص النظام:</h4>';
            
            results.forEach(result => {
                const indicator = result.success ? 'green' : 'red';
                html += `
                    <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <span class="status-indicator ${indicator}"></span>
                        <strong>${result.test}:</strong> ${result.status}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function displayFileResults(results) {
            const container = document.getElementById('file-status');
            let html = '<h4>حالة الملفات:</h4>';
            
            results.forEach(result => {
                const indicator = result.success ? 'green' : 'red';
                html += `
                    <div style="margin: 5px 0;">
                        <span class="status-indicator ${indicator}"></span>
                        ${result.test}: ${result.status}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function displayDataResults(results) {
            const container = document.getElementById('data-status');
            let html = '<h4>حالة البيانات:</h4>';
            
            results.forEach(result => {
                const indicator = result.success ? 'green' : 'red';
                html += `
                    <div style="margin: 5px 0;">
                        <span class="status-indicator ${indicator}"></span>
                        ${result.test}: ${result.status}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // تشغيل فحص تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkSystemStatus();
                checkData();
            }, 1000);
        });
    </script>
</body>
</html>
