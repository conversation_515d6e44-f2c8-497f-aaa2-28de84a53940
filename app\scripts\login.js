// ==================== تسجيل الدخول للنظام الرئيسي ====================

// بيانات تسجيل الدخول
const loginCredentials = {
    admin: {
        username: 'admin',
        password: 'admin123',
        name: 'مدير النظام',
        role: 'admin'
    },
    user: {
        username: 'user',
        password: 'user123',
        name: 'مستخدم عادي',
        role: 'user'
    }
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل واجهة تسجيل الدخول الرئيسية...');
    
    // التحقق من وجود جلسة نشطة
    checkExistingSession();
    
    // إعداد معالج النموذج
    setupFormHandler();
    
    // تركيز على أول حقل
    focusFirstInput();
    
    console.log('✅ تم تحميل واجهة تسجيل الدخول بنجاح');
});

// التحقق من وجود جلسة نشطة
function checkExistingSession() {
    const session = sessionStorage.getItem('gasSystemSession');
    
    if (session) {
        try {
            const sessionData = JSON.parse(session);
            if (sessionData.isActive) {
                showMessage('تم العثور على جلسة نشطة، جاري إعادة التوجيه...', 'success');
                setTimeout(() => {
                    window.location.href = 'main.html';
                }, 1500);
                return;
            }
        } catch (error) {
            console.log('خطأ في قراءة بيانات الجلسة:', error);
            sessionStorage.removeItem('gasSystemSession');
        }
    }
}

// إعداد معالج النموذج
function setupFormHandler() {
    const form = document.getElementById('login-form');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });
    
    // معالجة الضغط على Enter
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleLogin();
        }
    });
}

// تركيز على أول حقل إدخال
function focusFirstInput() {
    setTimeout(() => {
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.focus();
        }
    }, 100);
}

// معالجة تسجيل الدخول
function handleLogin() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const loginBtn = document.getElementById('login-btn');
    
    // التحقق من البيانات
    if (!username || !password) {
        showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
        return;
    }
    
    // عرض مؤشر التحميل
    setLoadingState(loginBtn, true);
    
    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // التحقق من صحة البيانات
        const user = Object.values(loginCredentials).find(u => 
            u.username === username && u.password === password
        );
        
        if (user) {
            // إنشاء جلسة المستخدم
            const session = {
                workshopId: 'main-system',
                userId: user.username,
                username: user.username,
                name: user.name,
                role: user.role,
                loginTime: new Date().toISOString(),
                isActive: true
            };
            
            // حفظ الجلسة
            sessionStorage.setItem('gasSystemSession', JSON.stringify(session));
            
            // عرض رسالة نجاح
            showMessage('تم تسجيل الدخول بنجاح! جاري إعادة التوجيه...', 'success');
            
            // إعادة التوجيه للنظام الرئيسي
            setTimeout(() => {
                window.location.href = 'main.html';
            }, 1500);
            
        } else {
            showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            setLoadingState(loginBtn, false);
        }
    }, 1000);
}

// تسجيل دخول سريع
function quickLogin() {
    // ملء البيانات التجريبية
    document.getElementById('username').value = loginCredentials.admin.username;
    document.getElementById('password').value = loginCredentials.admin.password;
    
    // تسجيل الدخول تلقائياً
    setTimeout(() => {
        handleLogin();
    }, 500);
}

// طلب تفعيل النظام
function requestActivation() {
    const modal = createModal('طلب تفعيل النظام', `
        <div class="activation-request">
            <div class="activation-icon">
                <i class="fas fa-key"></i>
            </div>
            <h3>طلب تفعيل النظام</h3>
            <p>لتفعيل النظام، يرجى إدخال كود التفعيل الذي حصلت عليه من المطور.</p>
            
            <form id="activation-form">
                <div class="form-group">
                    <label for="activation-code">كود التفعيل:</label>
                    <input type="text" id="activation-code" placeholder="أدخل كود التفعيل" required>
                </div>
                
                <div class="form-group">
                    <label for="workshop-name">اسم الورشة:</label>
                    <input type="text" id="workshop-name" placeholder="أدخل اسم الورشة" required>
                </div>
                
                <div class="form-group">
                    <label for="owner-name">اسم المالك:</label>
                    <input type="text" id="owner-name" placeholder="أدخل اسم المالك" required>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check"></i>
                        تفعيل النظام
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
            
            <div class="activation-help">
                <h4>كيفية الحصول على كود التفعيل:</h4>
                <ol>
                    <li>تواصل مع المطور أو الدعم الفني</li>
                    <li>قدم معلومات الورشة المطلوبة</li>
                    <li>احصل على كود التفعيل الخاص بك</li>
                    <li>أدخل الكود هنا لتفعيل النظام</li>
                </ol>
                
                <div class="contact-info">
                    <p><strong>📞 الهاتف:</strong> +213 555 123 456</p>
                    <p><strong>📧 البريد:</strong> <EMAIL></p>
                </div>
            </div>
        </div>
    `);
    
    // معالج نموذج التفعيل
    document.getElementById('activation-form').addEventListener('submit', function(e) {
        e.preventDefault();
        processActivation();
    });
}

// معالجة طلب التفعيل
function processActivation() {
    const activationCode = document.getElementById('activation-code').value.trim();
    const workshopName = document.getElementById('workshop-name').value.trim();
    const ownerName = document.getElementById('owner-name').value.trim();
    
    if (!activationCode || !workshopName || !ownerName) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // محاكاة معالجة طلب التفعيل
    const activationRequest = {
        id: Date.now().toString(),
        activationCode: activationCode,
        workshopName: workshopName,
        ownerName: ownerName,
        requestDate: new Date().toISOString(),
        status: 'pending'
    };
    
    // حفظ الطلب
    let requests = JSON.parse(localStorage.getItem('activationRequests')) || [];
    requests.push(activationRequest);
    localStorage.setItem('activationRequests', JSON.stringify(requests));
    
    // إغلاق النافذة وإظهار رسالة
    closeModal();
    showMessage('تم إرسال طلب التفعيل بنجاح! سيتم مراجعته قريباً.', 'success');
}

// عرض معلومات الدعم
function showSupport() {
    createModal('الدعم الفني', `
        <div class="support-info">
            <div class="support-icon">
                <i class="fas fa-headset"></i>
            </div>
            <h3>الدعم الفني</h3>
            
            <div class="contact-methods">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <div>
                        <strong>الهاتف</strong>
                        <p>+213 555 123 456</p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <div>
                        <strong>البريد الإلكتروني</strong>
                        <p><EMAIL></p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <div>
                        <strong>ساعات العمل</strong>
                        <p>السبت - الخميس (8:00 - 17:00)</p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <i class="fas fa-globe"></i>
                    <div>
                        <strong>الدعم الفني</strong>
                        <p>متاح 24/7</p>
                    </div>
                </div>
            </div>
            
            <div class="support-help">
                <h4>كيف يمكننا مساعدتك؟</h4>
                <ul>
                    <li>مشاكل تسجيل الدخول</li>
                    <li>طلبات تفعيل النظام</li>
                    <li>الدعم الفني العام</li>
                    <li>تدريب على استخدام النظام</li>
                    <li>تحديثات وصيانة</li>
                </ul>
            </div>
            
            <div class="form-actions">
                <button class="btn btn-primary" onclick="closeModal()">
                    <i class="fas fa-check"></i>
                    تم
                </button>
            </div>
        </div>
    `);
}

// عرض رسالة
function showMessage(message, type) {
    clearMessages();
    
    const messageElement = document.getElementById(`${type}-message`);
    if (messageElement) {
        messageElement.textContent = message;
        messageElement.style.display = 'block';
        
        // إخفاء الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 5000);
    }
}

// مسح الرسائل
function clearMessages() {
    document.getElementById('error-message').style.display = 'none';
    document.getElementById('success-message').style.display = 'none';
}

// تعيين حالة التحميل للزر
function setLoadingState(button, isLoading) {
    if (isLoading) {
        button.disabled = true;
        const originalContent = button.innerHTML;
        button.setAttribute('data-original-content', originalContent);
        button.innerHTML = '<div class="loading"></div> جاري تسجيل الدخول...';
    } else {
        button.disabled = false;
        const originalContent = button.getAttribute('data-original-content');
        if (originalContent) {
            button.innerHTML = originalContent;
        }
    }
}

// إنشاء نافذة منبثقة
function createModal(title, content) {
    // إزالة النافذة الموجودة إن وجدت
    const existingModal = document.getElementById('modal-overlay');
    if (existingModal) {
        existingModal.remove();
    }
    
    // إنشاء النافذة الجديدة
    const modal = document.createElement('div');
    modal.id = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    // إضافة الأنماط
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 2rem;
    `;
    
    const modalContent = modal.querySelector('.modal-content');
    modalContent.style.cssText = `
        background: white;
        border-radius: 16px;
        max-width: 500px;
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    `;
    
    document.body.appendChild(modal);
    
    return modal;
}

// إغلاق النافذة المنبثقة
function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.remove();
    }
}
