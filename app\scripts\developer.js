// ==================== لوحة تحكم المطور ====================

// بيانات النظام
let systemData = {
    workshops: JSON.parse(localStorage.getItem('systemWorkshops')) || [],
    licenses: JSON.parse(localStorage.getItem('systemLicenses')) || [],
    currentSection: 'overview'
};

// تهيئة لوحة المطور
document.addEventListener('DOMContentLoaded', function() {
    initializeDeveloperPanel();
    loadSystemData();
    showSection('overview');
    setupEventListeners();
});

// تهيئة لوحة المطور
function initializeDeveloperPanel() {
    console.log('🔧 تهيئة لوحة تحكم المطور...');
    
    // التحقق من صلاحية الدخول
    const developerSession = sessionStorage.getItem('developerSession');
    if (!developerSession) {
        window.location.href = 'login.html';
        return;
    }
    
    updateSystemStats();
    loadWorkshopsTable();
    setupLicenseGenerator();
}

// تحميل بيانات النظام
function loadSystemData() {
    systemData.workshops = JSON.parse(localStorage.getItem('systemWorkshops')) || [];
    systemData.licenses = JSON.parse(localStorage.getItem('systemLicenses')) || [];
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // مولد التراخيص
    const licenseForm = document.getElementById('license-generator-form');
    if (licenseForm) {
        licenseForm.addEventListener('submit', handleLicenseGeneration);
    }
    
    // تغيير نوع الترخيص
    const licenseTypeSelect = document.getElementById('license-type');
    if (licenseTypeSelect) {
        licenseTypeSelect.addEventListener('change', updateLicenseDuration);
    }
}

// إظهار قسم معين
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // إزالة التفعيل من جميع عناصر التنقل
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // إظهار القسم المحدد
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // تفعيل عنصر التنقل المحدد
    const navItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`).parentElement;
    if (navItem) {
        navItem.classList.add('active');
    }
    
    systemData.currentSection = sectionName;
    
    // تحديث المحتوى حسب القسم
    switch (sectionName) {
        case 'overview':
            updateSystemStats();
            break;
        case 'workshops':
            loadWorkshopsTable();
            break;
        case 'licenses':
            setupLicenseGenerator();
            break;
    }
}

// تحديث إحصائيات النظام
function updateSystemStats() {
    const stats = getSystemStats();
    
    // تحديث البطاقات
    document.getElementById('total-workshops').textContent = stats.totalWorkshops;
    document.getElementById('active-workshops').textContent = stats.activeWorkshops;
    document.getElementById('expired-licenses').textContent = stats.expiredLicenses;
    document.getElementById('total-users').textContent = stats.totalUsers;
    
    // تحديث توزيع التراخيص
    updateLicenseDistribution(stats);
    
    // تحديث النشاط الأخير
    updateRecentActivity();
}

// الحصول على إحصائيات النظام
function getSystemStats() {
    const now = new Date();
    
    return {
        totalWorkshops: systemData.workshops.length,
        activeWorkshops: systemData.workshops.filter(w => w.isActive && isLicenseValid(w)).length,
        expiredLicenses: systemData.workshops.filter(w => !isLicenseValid(w)).length,
        totalUsers: systemData.workshops.reduce((total, w) => total + (w.users ? w.users.length : 0), 0),
        trialWorkshops: systemData.workshops.filter(w => w.licenseType === 'trial').length,
        professionalWorkshops: systemData.workshops.filter(w => w.licenseType === 'professional').length,
        enterpriseWorkshops: systemData.workshops.filter(w => w.licenseType === 'enterprise').length
    };
}

// التحقق من صلاحية الترخيص
function isLicenseValid(workshop) {
    if (!workshop.expiryDate) return false;
    return new Date(workshop.expiryDate) > new Date();
}

// تحديث توزيع التراخيص
function updateLicenseDistribution(stats) {
    const total = stats.totalWorkshops || 1;
    
    // حساب النسب
    const trialPercent = (stats.trialWorkshops / total) * 100;
    const professionalPercent = (stats.professionalWorkshops / total) * 100;
    const enterprisePercent = (stats.enterpriseWorkshops / total) * 100;
    
    // تحديث الأشرطة
    document.getElementById('trial-bar').style.width = `${trialPercent}%`;
    document.getElementById('professional-bar').style.width = `${professionalPercent}%`;
    document.getElementById('enterprise-bar').style.width = `${enterprisePercent}%`;
    
    // تحديث الأرقام
    document.getElementById('trial-count').textContent = stats.trialWorkshops;
    document.getElementById('professional-count').textContent = stats.professionalWorkshops;
    document.getElementById('enterprise-count').textContent = stats.enterpriseWorkshops;
}

// تحديث النشاط الأخير
function updateRecentActivity() {
    const activityList = document.getElementById('recent-activity');
    if (!activityList) return;
    
    const activities = [];
    
    // إضافة أنشطة الورشات
    systemData.workshops.forEach(workshop => {
        if (workshop.lastLogin) {
            activities.push({
                type: 'login',
                workshop: workshop.name,
                time: workshop.lastLogin,
                icon: 'fas fa-sign-in-alt'
            });
        }
        
        activities.push({
            type: 'created',
            workshop: workshop.name,
            time: workshop.createdAt,
            icon: 'fas fa-plus'
        });
    });
    
    // ترتيب حسب الوقت
    activities.sort((a, b) => new Date(b.time) - new Date(a.time));
    
    // عرض آخر 5 أنشطة
    const recentActivities = activities.slice(0, 5);
    
    activityList.innerHTML = recentActivities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <h4>${activity.type === 'login' ? 'تسجيل دخول' : 'إنشاء ورشة'}</h4>
                <p>${activity.workshop} - ${formatDate(activity.time)}</p>
            </div>
        </div>
    `).join('');
}

// تحميل جدول الورشات
function loadWorkshopsTable() {
    const tableBody = document.getElementById('workshops-table');
    if (!tableBody) return;
    
    tableBody.innerHTML = systemData.workshops.map(workshop => {
        const isValid = isLicenseValid(workshop);
        const statusClass = isValid ? 'active' : 'expired';
        const statusText = isValid ? 'نشط' : 'منتهي الصلاحية';
        
        return `
            <tr>
                <td><span class="workshop-code">${workshop.code}</span></td>
                <td>${workshop.name}</td>
                <td>${workshop.owner}</td>
                <td>${getLicenseTypeText(workshop.licenseType)}</td>
                <td>${formatDate(workshop.expiryDate)}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="editWorkshop('${workshop.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="extendLicense('${workshop.id}')">
                            <i class="fas fa-clock"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteWorkshop('${workshop.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// إعداد مولد التراخيص
function setupLicenseGenerator() {
    updateLicenseDuration();
}

// تحديث مدة الترخيص
function updateLicenseDuration() {
    const licenseType = document.getElementById('license-type').value;
    const durationInput = document.getElementById('license-duration');
    
    if (!durationInput) return;
    
    switch (licenseType) {
        case 'trial':
            durationInput.value = 30;
            break;
        case 'professional':
            durationInput.value = 365;
            break;
        case 'enterprise':
            durationInput.value = 3650; // 10 سنوات
            break;
    }
}

// معالجة إنشاء ترخيص
function handleLicenseGeneration(e) {
    e.preventDefault();
    
    const licenseType = document.getElementById('license-type').value;
    const duration = parseInt(document.getElementById('license-duration').value);
    const workshopName = document.getElementById('workshop-name').value;
    const ownerName = document.getElementById('owner-name').value;
    
    // إنشاء الترخيص
    const license = generateNewLicense(licenseType, duration, workshopName, ownerName);
    
    // عرض الترخيص المُنشأ
    displayGeneratedLicense(license);
    
    showToast('تم إنشاء الترخيص بنجاح', 'success');
}

// إنشاء ترخيص جديد
function generateNewLicense(type, duration, workshopName, ownerName) {
    const workshopCode = generateWorkshopCode();
    const licenseKey = generateLicenseKey();
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + duration);
    
    const workshop = {
        id: generateId(),
        code: workshopCode,
        name: workshopName,
        owner: ownerName,
        phone: '',
        address: '',
        email: '',
        licenseType: type,
        licenseKey: licenseKey,
        expiryDate: expiryDate.toISOString(),
        isActive: true,
        users: [
            {
                id: generateId(),
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                permissions: getPermissionsByLicenseType(type),
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ],
        createdAt: new Date().toISOString(),
        lastLogin: null
    };
    
    // إضافة الورشة للنظام
    systemData.workshops.push(workshop);
    saveSystemData();
    
    return workshop;
}

// عرض الترخيص المُنشأ
function displayGeneratedLicense(license) {
    const generatedLicenseDiv = document.getElementById('generated-license');
    if (!generatedLicenseDiv) return;
    
    document.getElementById('generated-key').value = license.licenseKey;
    document.getElementById('generated-code').textContent = license.code;
    document.getElementById('generated-type').textContent = getLicenseTypeText(license.licenseType);
    document.getElementById('generated-expiry').textContent = formatDate(license.expiryDate);
    
    generatedLicenseDiv.style.display = 'block';
}

// نسخ مفتاح الترخيص
function copyLicenseKey() {
    const keyInput = document.getElementById('generated-key');
    keyInput.select();
    document.execCommand('copy');
    showToast('تم نسخ مفتاح الترخيص', 'success');
}

// تحديث الإحصائيات
function refreshStats() {
    loadSystemData();
    updateSystemStats();
    loadWorkshopsTable();
    showToast('تم تحديث الإحصائيات', 'success');
}

// دوال مساعدة
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateWorkshopCode() {
    const prefix = 'WS';
    const year = new Date().getFullYear().toString().slice(-2);
    const sequence = (systemData.workshops.length + 1).toString().padStart(3, '0');
    return `${prefix}-${year}-${sequence}`;
}

function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 20; i++) {
        if (i > 0 && i % 4 === 0) result += '-';
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

function getLicenseTypeText(type) {
    const types = {
        'trial': 'تجريبي',
        'professional': 'احترافي',
        'enterprise': 'مؤسسي'
    };
    return types[type] || type;
}

function getPermissionsByLicenseType(type) {
    const permissions = {
        'trial': ['customers', 'cards', 'appointments'],
        'professional': ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports'],
        'enterprise': ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports', 'employees', 'payroll', 'maintenance']
    };
    return permissions[type] || permissions.trial;
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

function saveSystemData() {
    localStorage.setItem('systemWorkshops', JSON.stringify(systemData.workshops));
    localStorage.setItem('systemLicenses', JSON.stringify(systemData.licenses));
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        sessionStorage.removeItem('developerSession');
        window.location.href = 'login.html';
    }
}

// إظهار رسالة تنبيه
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = type === 'success' ? 'fas fa-check-circle' : 
                 type === 'error' ? 'fas fa-exclamation-circle' : 
                 type === 'warning' ? 'fas fa-exclamation-triangle' : 
                 'fas fa-info-circle';
    
    toast.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
    `;
    
    toastContainer.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// ==================== دوال إضافية للوحة المطور ====================

// تحرير ورشة
function editWorkshop(workshopId) {
    const workshop = systemData.workshops.find(w => w.id === workshopId);
    if (!workshop) return;

    const modalBody = `
        <form id="edit-workshop-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="edit-workshop-name">اسم الورشة</label>
                    <input type="text" id="edit-workshop-name" value="${workshop.name}" required>
                </div>
                <div class="form-group">
                    <label for="edit-workshop-owner">اسم المالك</label>
                    <input type="text" id="edit-workshop-owner" value="${workshop.owner}" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="edit-workshop-phone">الهاتف</label>
                    <input type="tel" id="edit-workshop-phone" value="${workshop.phone || ''}">
                </div>
                <div class="form-group">
                    <label for="edit-workshop-email">البريد الإلكتروني</label>
                    <input type="email" id="edit-workshop-email" value="${workshop.email || ''}">
                </div>
            </div>
            <div class="form-group">
                <label for="edit-workshop-address">العنوان</label>
                <input type="text" id="edit-workshop-address" value="${workshop.address || ''}">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="edit-workshop-license">نوع الترخيص</label>
                    <select id="edit-workshop-license">
                        <option value="trial" ${workshop.licenseType === 'trial' ? 'selected' : ''}>تجريبي</option>
                        <option value="professional" ${workshop.licenseType === 'professional' ? 'selected' : ''}>احترافي</option>
                        <option value="enterprise" ${workshop.licenseType === 'enterprise' ? 'selected' : ''}>مؤسسي</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-workshop-status">الحالة</label>
                    <select id="edit-workshop-status">
                        <option value="true" ${workshop.isActive ? 'selected' : ''}>نشط</option>
                        <option value="false" ${!workshop.isActive ? 'selected' : ''}>معطل</option>
                    </select>
                </div>
            </div>
            <div class="form-actions" style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    `;

    showModal('تحرير الورشة', modalBody);

    document.getElementById('edit-workshop-form').addEventListener('submit', (e) => {
        e.preventDefault();

        workshop.name = document.getElementById('edit-workshop-name').value;
        workshop.owner = document.getElementById('edit-workshop-owner').value;
        workshop.phone = document.getElementById('edit-workshop-phone').value;
        workshop.email = document.getElementById('edit-workshop-email').value;
        workshop.address = document.getElementById('edit-workshop-address').value;
        workshop.licenseType = document.getElementById('edit-workshop-license').value;
        workshop.isActive = document.getElementById('edit-workshop-status').value === 'true';

        saveSystemData();
        loadWorkshopsTable();
        updateSystemStats();
        hideModal();
        showToast('تم تحديث بيانات الورشة', 'success');
    });
}

// تمديد الترخيص
function extendLicense(workshopId) {
    const workshop = systemData.workshops.find(w => w.id === workshopId);
    if (!workshop) return;

    const modalBody = `
        <form id="extend-license-form">
            <div class="form-group">
                <label>الورشة: <strong>${workshop.name}</strong></label>
                <label>الترخيص الحالي: <strong>${getLicenseTypeText(workshop.licenseType)}</strong></label>
                <label>تاريخ الانتهاء الحالي: <strong>${formatDate(workshop.expiryDate)}</strong></label>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="extend-duration">مدة التمديد (بالأيام)</label>
                    <input type="number" id="extend-duration" value="30" min="1" required>
                </div>
                <div class="form-group">
                    <label for="extend-type">نوع الترخيص الجديد</label>
                    <select id="extend-type">
                        <option value="trial">تجريبي</option>
                        <option value="professional" selected>احترافي</option>
                        <option value="enterprise">مؤسسي</option>
                    </select>
                </div>
            </div>
            <div class="form-actions" style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
                <button type="submit" class="btn btn-warning">تمديد الترخيص</button>
            </div>
        </form>
    `;

    showModal('تمديد الترخيص', modalBody);

    document.getElementById('extend-license-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const duration = parseInt(document.getElementById('extend-duration').value);
        const newType = document.getElementById('extend-type').value;

        const currentExpiry = new Date(workshop.expiryDate);
        const newExpiry = new Date(currentExpiry);
        newExpiry.setDate(newExpiry.getDate() + duration);

        workshop.expiryDate = newExpiry.toISOString();
        workshop.licenseType = newType;
        workshop.licenseKey = generateLicenseKey(); // مفتاح جديد

        saveSystemData();
        loadWorkshopsTable();
        updateSystemStats();
        hideModal();
        showToast(`تم تمديد الترخيص لـ ${duration} يوم`, 'success');
    });
}

// حذف ورشة
function deleteWorkshop(workshopId) {
    const workshop = systemData.workshops.find(w => w.id === workshopId);
    if (!workshop) return;

    if (confirm(`هل أنت متأكد من حذف الورشة "${workshop.name}"؟\nهذا الإجراء لا يمكن التراجع عنه!`)) {
        systemData.workshops = systemData.workshops.filter(w => w.id !== workshopId);
        saveSystemData();
        loadWorkshopsTable();
        updateSystemStats();
        showToast('تم حذف الورشة', 'success');
    }
}

// إضافة ورشة جديدة
function showAddWorkshopModal() {
    const modalBody = `
        <form id="add-workshop-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="new-workshop-name">اسم الورشة *</label>
                    <input type="text" id="new-workshop-name" required>
                </div>
                <div class="form-group">
                    <label for="new-workshop-owner">اسم المالك *</label>
                    <input type="text" id="new-workshop-owner" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="new-workshop-phone">الهاتف</label>
                    <input type="tel" id="new-workshop-phone">
                </div>
                <div class="form-group">
                    <label for="new-workshop-email">البريد الإلكتروني</label>
                    <input type="email" id="new-workshop-email">
                </div>
            </div>
            <div class="form-group">
                <label for="new-workshop-address">العنوان</label>
                <input type="text" id="new-workshop-address">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="new-workshop-license">نوع الترخيص</label>
                    <select id="new-workshop-license">
                        <option value="trial">تجريبي (30 يوم)</option>
                        <option value="professional">احترافي (سنة)</option>
                        <option value="enterprise">مؤسسي (10 سنوات)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="new-workshop-duration">مدة الترخيص (أيام)</label>
                    <input type="number" id="new-workshop-duration" value="30" min="1">
                </div>
            </div>
            <div class="form-actions" style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
                <button type="submit" class="btn btn-success">إنشاء الورشة</button>
            </div>
        </form>
    `;

    showModal('إضافة ورشة جديدة', modalBody);

    // تحديث المدة عند تغيير نوع الترخيص
    document.getElementById('new-workshop-license').addEventListener('change', (e) => {
        const durationInput = document.getElementById('new-workshop-duration');
        switch (e.target.value) {
            case 'trial':
                durationInput.value = 30;
                break;
            case 'professional':
                durationInput.value = 365;
                break;
            case 'enterprise':
                durationInput.value = 3650;
                break;
        }
    });

    document.getElementById('add-workshop-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const workshopData = {
            name: document.getElementById('new-workshop-name').value,
            owner: document.getElementById('new-workshop-owner').value,
            phone: document.getElementById('new-workshop-phone').value,
            email: document.getElementById('new-workshop-email').value,
            address: document.getElementById('new-workshop-address').value,
            licenseType: document.getElementById('new-workshop-license').value,
            duration: parseInt(document.getElementById('new-workshop-duration').value)
        };

        const newWorkshop = generateNewLicense(
            workshopData.licenseType,
            workshopData.duration,
            workshopData.name,
            workshopData.owner
        );

        // تحديث البيانات الإضافية
        newWorkshop.phone = workshopData.phone;
        newWorkshop.email = workshopData.email;
        newWorkshop.address = workshopData.address;

        saveSystemData();
        loadWorkshopsTable();
        updateSystemStats();
        hideModal();
        showToast(`تم إنشاء الورشة بكود: ${newWorkshop.code}`, 'success');
    });
}

// إنشاء نسخة احتياطية
function createBackup() {
    const backupData = {
        workshops: systemData.workshops,
        licenses: systemData.licenses,
        backupDate: new Date().toISOString(),
        systemVersion: '1.0.0',
        totalWorkshops: systemData.workshops.length,
        activeWorkshops: systemData.workshops.filter(w => w.isActive && isLicenseValid(w)).length
    };

    const dataStr = JSON.stringify(backupData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `gas-system-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showToast('تم إنشاء النسخة الاحتياطية وتحميلها', 'success');
}

// معالجة استيراد الملف
function handleFileImport(input) {
    const file = input.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);

            if (importedData.workshops && Array.isArray(importedData.workshops)) {
                if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية!')) {
                    systemData.workshops = importedData.workshops;
                    systemData.licenses = importedData.licenses || [];

                    saveSystemData();
                    loadWorkshopsTable();
                    updateSystemStats();
                    showToast('تم استيراد البيانات بنجاح', 'success');
                }
            } else {
                showToast('ملف البيانات غير صالح', 'error');
            }
        } catch (error) {
            showToast('خطأ في قراءة ملف البيانات', 'error');
        }
    };
    reader.readAsText(file);

    // إعادة تعيين قيمة الإدخال
    input.value = '';
}

// إعادة تعيين النظام
function resetSystem() {
    if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع الورشات والتراخيص نهائياً!')) {
            localStorage.removeItem('systemWorkshops');
            localStorage.removeItem('systemLicenses');

            systemData.workshops = [];
            systemData.licenses = [];

            loadWorkshopsTable();
            updateSystemStats();
            showToast('تم إعادة تعيين النظام', 'warning');
        }
    }
}

// إظهار النافذة المنبثقة
function showModal(title, body) {
    document.getElementById('modal-title').textContent = title;
    document.getElementById('modal-body').innerHTML = body;
    document.getElementById('modal-overlay').style.display = 'flex';
}

// إخفاء النافذة المنبثقة
function hideModal() {
    document.getElementById('modal-overlay').style.display = 'none';
}

// إغلاق النافذة عند النقر خارجها
document.addEventListener('click', function(e) {
    if (e.target.id === 'modal-overlay') {
        hideModal();
    }
});

console.log('✅ تم تحميل لوحة تحكم المطور بنجاح');
