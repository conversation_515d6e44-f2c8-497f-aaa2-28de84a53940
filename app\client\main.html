<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-gas-pump"></i>
                <h1>مؤسسة وقود المستقبل</h1>
            </div>
            <div class="header-actions">
                <button id="dark-mode-btn" class="btn-icon" title="الوضع المظلم">
                    <i class="fas fa-moon"></i>
                </button>
                <button id="notifications-btn" class="btn-icon" title="الإشعارات">
                    <i class="fas fa-bell"></i>
                    <span class="badge hidden" id="notifications-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- الشريط الجانبي -->
    <nav class="sidebar">
        <ul class="nav-menu">
            <li>
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li>
                <a href="#customers" class="nav-link" data-section="customers">
                    <i class="fas fa-users"></i>
                    <span>الزبائن</span>
                </a>
            </li>
            <li>
                <a href="#gas-cards" class="nav-link" data-section="gas-cards">
                    <i class="fas fa-credit-card"></i>
                    <span>بطاقات الغاز</span>
                </a>
            </li>
            <li>
                <a href="#appointments" class="nav-link" data-section="appointments">
                    <i class="fas fa-calendar"></i>
                    <span>المواعيد</span>
                </a>
            </li>
            <li>
                <a href="#transmission" class="nav-link" data-section="transmission">
                    <i class="fas fa-table"></i>
                    <span>جدول الإرسال</span>
                </a>
            </li>
            <li>
                <a href="#certificates" class="nav-link" data-section="certificates">
                    <i class="fas fa-certificate"></i>
                    <span>الشهادات</span>
                </a>
            </li>
            <li>
                <a href="#inventory" class="nav-link" data-section="inventory">
                    <i class="fas fa-boxes"></i>
                    <span>المخزون</span>
                </a>
            </li>
            <li>
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
            </li>
            <li>
                <a href="#suppliers" class="nav-link" data-section="suppliers">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li>
                <a href="#sales" class="nav-link" data-section="sales">
                    <i class="fas fa-shopping-cart"></i>
                    <span>المبيعات</span>
                </a>
            </li>
            <li>
                <a href="#purchases" class="nav-link" data-section="purchases">
                    <i class="fas fa-shopping-bag"></i>
                    <span>المشتريات</span>
                </a>
            </li>
            <li>
                <a href="#debts" class="nav-link" data-section="debts">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>الديون</span>
                </a>
            </li>
            <li>
                <a href="#employees" class="nav-link" data-section="employees">
                    <i class="fas fa-users"></i>
                    <span>الموظفين</span>
                </a>
            </li>
            <li>
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- قسم الرئيسية -->
        <section id="dashboard" class="section active">
            <h2>لوحة التحكم</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-customers">0</h3>
                        <p>إجمالي الزبائن</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-cards">0</h3>
                        <p>بطاقات الغاز</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-appointments">0</h3>
                        <p>المواعيد</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-table"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-transmissions">0</h3>
                        <p>عمليات الإرسال</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم الزبائن -->
        <section id="customers" class="section">
            <div class="section-header">
                <h2>إدارة الزبائن</h2>
                <div class="header-actions">
                    <button id="add-customer-btn" class="btn btn-primary" onclick="addCustomer()">
                        <i class="fas fa-plus"></i> إضافة زبون
                    </button>
                    <button id="print-customers-btn" class="btn btn-secondary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="export-customers-pdf-btn" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                    <button id="import-customers-btn" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="search-bar">
                    <input type="text" id="search-customers" placeholder="بحث في الزبائن (الاسم، الهاتف، العنوان)...">
                    <button class="btn btn-secondary" onclick="searchCustomers()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="clearCustomersSearch()">
                        <i class="fas fa-times"></i> مسح
                    </button>
                </div>
                <div class="filter-options">
                    <select id="customers-filter" onchange="filterCustomers()">
                        <option value="all">جميع الزبائن</option>
                        <option value="recent">الزبائن الجدد</option>
                        <option value="active">الزبائن النشطين</option>
                        <option value="inactive">الزبائن غير النشطين</option>
                    </select>
                    <select id="customers-sort" onchange="sortCustomers()">
                        <option value="name">ترتيب حسب الاسم</option>
                        <option value="date">ترتيب حسب التاريخ</option>
                        <option value="activity">ترتيب حسب النشاط</option>
                    </select>
                </div>
            </div>

            <div class="customers-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-customers">0</h3>
                        <p>زبائن نشطين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="new-customers-month">0</h3>
                        <p>زبائن جدد هذا الشهر</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-vehicles">0</h3>
                        <p>إجمالي السيارات</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="customers-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all-customers" onchange="selectAllCustomers()">
                            </th>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>عدد السيارات</th>
                            <th>آخر زيارة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="bulk-actions" id="bulk-actions" style="display: none;">
                <button class="btn btn-danger" onclick="deleteSelectedCustomers()">
                    <i class="fas fa-trash"></i> حذف المحدد
                </button>
                <button class="btn btn-secondary" onclick="exportSelectedCustomers()">
                    <i class="fas fa-download"></i> تصدير المحدد
                </button>
                <button class="btn btn-primary" onclick="sendBulkNotification()">
                    <i class="fas fa-bell"></i> إرسال إشعار جماعي
                </button>
            </div>
        </section>

        <!-- قسم بطاقات الغاز -->
        <section id="gas-cards" class="section">
            <div class="section-header">
                <h2>بطاقات الغاز</h2>
                <div class="header-actions">
                    <button id="add-card-btn" class="btn btn-primary" onclick="addCard()">
                        <i class="fas fa-plus"></i> إضافة بطاقة
                    </button>
                    <button id="renew-cards-btn" class="btn btn-warning">
                        <i class="fas fa-sync"></i> تجديد جماعي
                    </button>
                    <button id="print-cards-btn" class="btn btn-secondary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="export-cards-pdf-btn" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="search-bar">
                    <input type="text" id="search-cards" placeholder="بحث في البطاقات (الزبون، رقم السيارة، رقم البطاقة)...">
                    <button class="btn btn-secondary" onclick="searchCards()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="clearCardsSearch()">
                        <i class="fas fa-times"></i> مسح
                    </button>
                </div>
                <div class="filter-options">
                    <select id="cards-status-filter" onchange="filterCardsByStatus()">
                        <option value="all">جميع البطاقات</option>
                        <option value="active">نشطة</option>
                        <option value="expired">منتهية الصلاحية</option>
                        <option value="expiring-soon">تنتهي قريباً</option>
                    </select>
                    <input type="date" id="cards-date-filter" onchange="filterCardsByDate()" placeholder="تصفية حسب التاريخ">
                </div>
            </div>

            <div class="cards-stats">
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-cards">0</h3>
                        <p>بطاقات نشطة</p>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="expired-cards">0</h3>
                        <p>بطاقات منتهية</p>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="expiring-cards">0</h3>
                        <p>تنتهي خلال 30 يوم</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="cards-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all-cards" onchange="selectAllCards()">
                            </th>
                            <th>اسم الزبون</th>
                            <th>رقم السيارة</th>
                            <th>رقم البطاقة</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الأيام المتبقية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="bulk-actions" id="cards-bulk-actions" style="display: none;">
                <button class="btn btn-warning" onclick="renewSelectedCards()">
                    <i class="fas fa-sync"></i> تجديد المحدد
                </button>
                <button class="btn btn-danger" onclick="deleteSelectedCards()">
                    <i class="fas fa-trash"></i> حذف المحدد
                </button>
                <button class="btn btn-secondary" onclick="exportSelectedCards()">
                    <i class="fas fa-download"></i> تصدير المحدد
                </button>
            </div>
        </section>

        <!-- قسم المواعيد -->
        <section id="appointments" class="section">
            <div class="section-header">
                <h2>المواعيد</h2>
                <button id="add-appointment-btn" class="btn btn-primary" onclick="addAppointment()">
                    <i class="fas fa-plus"></i> إضافة موعد
                </button>
            </div>
            <div class="table-container">
                <table id="appointments-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>اسم الزبون</th>
                            <th>نوع الخدمة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم جدول الإرسال -->
        <section id="transmission" class="section">
            <div class="transmission-header">
                <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                <h2>مركز وقود المستقبل - عزيري عبد الله اسحاق</h2>
                <p>رقم: 463/2019</p>
                <p>إلى السيد: مدير الصناعة و المناجم لولاية المدية</p>
                <p class="highlight"><strong>جدول إرسال</strong></p>
                <p>تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات المجهزة بغاز البترول المميع شهر <span id="current-month-year"></span>.</p>
            </div>

            <!-- أدوات التحكم -->
            <div class="transmission-controls">
                <div class="controls-left">
                    <button type="button" class="btn btn-primary" onclick="showAddTransmissionModal()">
                        <i class="fas fa-plus"></i> إضافة عملية جديدة
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="printTransmissionTable()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="exportTransmissionToPDF()">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-danger" onclick="clearTransmissionTable()">
                        <i class="fas fa-trash"></i> مسح الجدول
                    </button>
                </div>
                <div class="controls-right">
                    <div class="search-box">
                        <input type="text" id="transmission-search-input" placeholder="بحث في الجدول...">
                        <button type="button" onclick="searchTransmissionTable()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- فلاتر -->
            <div class="transmission-filters">
                <div class="filter-group">
                    <label for="transmission-type-filter">نوع العملية:</label>
                    <select id="transmission-type-filter" onchange="filterTransmissionTable()">
                        <option value="all">الكل</option>
                        <option value="تركيب">تركيب</option>
                        <option value="مراقبة">مراقبة</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="transmission-month-filter">الشهر:</label>
                    <select id="transmission-month-filter" onchange="filterTransmissionTable()">
                        <option value="all">الكل</option>
                        <option value="current">الشهر الحالي</option>
                        <option value="last">الشهر الماضي</option>
                    </select>
                </div>
            </div>

            <!-- الجدول -->
            <div class="transmission-table-container">
                <table id="transmission-main-table">
                    <thead>
                        <tr>
                            <th>تركيب أو مراقبة</th>
                            <th>رقم خزان الغاز</th>
                            <th>الصنف</th>
                            <th>الرقم التسلسلي</th>
                            <th>رقم التسجيل</th>
                            <th>الإسم و اللقب</th>
                            <th>الرقم</th>
                            <th>تاريخ العملية</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transmission-table-body">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>

                <div id="transmission-empty-state" class="empty-state" style="display: none;">
                    <i class="fas fa-table"></i>
                    <h3>لا توجد بيانات</h3>
                    <p>لم يتم إضافة أي عمليات إلى جدول الإرسال بعد</p>
                    <button type="button" class="btn btn-primary" onclick="showAddTransmissionModal()">
                        <i class="fas fa-plus"></i> إضافة أول عملية
                    </button>
                </div>
            </div>

            <!-- ملخص الإحصائيات -->
            <div class="transmission-summary">
                <div class="summary-card">
                    <h4>إجمالي العمليات</h4>
                    <div class="count" id="transmission-total-count">0</div>
                </div>
                <div class="summary-card">
                    <h4>عمليات التركيب</h4>
                    <div class="count" id="transmission-installation-count">0</div>
                </div>
                <div class="summary-card">
                    <h4>عمليات المراقبة</h4>
                    <div class="count" id="transmission-monitoring-count">0</div>
                </div>
                <div class="summary-card">
                    <h4>عمليات هذا الشهر</h4>
                    <div class="count" id="transmission-current-month-count">0</div>
                </div>
            </div>
        </section>

        <!-- قسم الموردين -->
        <section id="suppliers" class="section">
            <div class="section-header">
                <h2>إدارة الموردين</h2>
                <div class="header-actions">
                    <button id="add-supplier-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مورد
                    </button>
                    <button id="print-suppliers-btn" class="btn btn-secondary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="export-suppliers-pdf-btn" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="search-bar">
                    <input type="text" id="search-suppliers" placeholder="بحث في الموردين...">
                    <button class="btn btn-secondary" onclick="searchSuppliers()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-secondary" onclick="clearSuppliersSearch()">
                        <i class="fas fa-times"></i> مسح
                    </button>
                </div>
                <div class="filter-options">
                    <select id="suppliers-status-filter" onchange="filterSuppliers()">
                        <option value="all">جميع الموردين</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                    <select id="suppliers-category-filter" onchange="filterSuppliers()">
                        <option value="all">جميع الفئات</option>
                        <option value="قطع غيار">قطع غيار</option>
                        <option value="أدوات">أدوات</option>
                        <option value="مواد خام">مواد خام</option>
                    </select>
                </div>
            </div>

            <div class="suppliers-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-suppliers">0</h3>
                        <p>موردين نشطين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-supplier-debt">0</h3>
                        <p>إجمالي المديونية</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="monthly-purchases">0</h3>
                        <p>مشتريات الشهر</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="suppliers-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all-suppliers" onchange="selectAllSuppliers()">
                            </th>
                            <th>اسم المورد</th>
                            <th>الشركة</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>الفئة</th>
                            <th>الرصيد</th>
                            <th>آخر تعامل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم المبيعات -->
        <section id="sales" class="section">
            <div class="section-header">
                <h2>إدارة المبيعات</h2>
                <div class="header-actions">
                    <button id="add-sale-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة عملية بيع
                    </button>
                    <button id="print-sales-btn" class="btn btn-secondary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="export-sales-pdf-btn" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="search-bar">
                    <input type="text" id="search-sales" placeholder="بحث في المبيعات...">
                    <button class="btn btn-secondary" onclick="searchSales()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="filter-options">
                    <input type="date" id="sales-date-from" onchange="filterSales()">
                    <input type="date" id="sales-date-to" onchange="filterSales()">
                    <select id="sales-status-filter" onchange="filterSales()">
                        <option value="all">جميع الحالات</option>
                        <option value="مكتملة">مكتملة</option>
                        <option value="معلقة">معلقة</option>
                        <option value="ملغية">ملغية</option>
                    </select>
                </div>
            </div>

            <div class="sales-stats">
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="daily-sales">0</h3>
                        <p>مبيعات اليوم</p>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="weekly-sales">0</h3>
                        <p>مبيعات الأسبوع</p>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="monthly-sales">0</h3>
                        <p>مبيعات الشهر</p>
                    </div>
                </div>
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-revenue">0</h3>
                        <p>إجمالي الإيرادات</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="sales-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>الزبون</th>
                            <th>الأصناف</th>
                            <th>الكمية</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم المشتريات -->
        <section id="purchases" class="section">
            <div class="section-header">
                <h2>إدارة المشتريات</h2>
                <div class="header-actions">
                    <button id="add-purchase-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة عملية شراء
                    </button>
                    <button id="print-purchases-btn" class="btn btn-secondary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="export-purchases-pdf-btn" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="search-bar">
                    <input type="text" id="search-purchases" placeholder="بحث في المشتريات...">
                    <button class="btn btn-secondary" onclick="searchPurchases()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="filter-options">
                    <input type="date" id="purchases-date-from" onchange="filterPurchases()">
                    <input type="date" id="purchases-date-to" onchange="filterPurchases()">
                    <select id="purchases-supplier-filter" onchange="filterPurchases()">
                        <option value="all">جميع الموردين</option>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </select>
                </div>
            </div>

            <div class="purchases-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="daily-purchases">0</h3>
                        <p>مشتريات اليوم</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="weekly-purchases">0</h3>
                        <p>مشتريات الأسبوع</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="monthly-purchases-amount">0</h3>
                        <p>مشتريات الشهر</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="purchases-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المورد</th>
                            <th>الأصناف</th>
                            <th>الكمية</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم الديون -->
        <section id="debts" class="section">
            <div class="section-header">
                <h2>إدارة الديون</h2>
                <div class="header-actions">
                    <button id="add-debt-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة دين
                    </button>
                    <button id="print-debts-btn" class="btn btn-secondary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="export-debts-pdf-btn" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="search-bar">
                    <input type="text" id="search-debts" placeholder="بحث في الديون...">
                    <button class="btn btn-secondary" onclick="searchDebts()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="filter-options">
                    <select id="debts-type-filter" onchange="filterDebts()">
                        <option value="all">جميع الأنواع</option>
                        <option value="لنا">لنا</option>
                        <option value="علينا">علينا</option>
                    </select>
                    <select id="debts-status-filter" onchange="filterDebts()">
                        <option value="all">جميع الحالات</option>
                        <option value="مستحق">مستحق</option>
                        <option value="متأخر">متأخر</option>
                        <option value="مسدد">مسدد</option>
                    </select>
                </div>
            </div>

            <div class="debts-stats">
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="debts-for-us">0</h3>
                        <p>ديون لنا</p>
                    </div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="debts-on-us">0</h3>
                        <p>ديون علينا</p>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="overdue-debts">0</h3>
                        <p>ديون متأخرة</p>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="net-balance">0</h3>
                        <p>الرصيد الصافي</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="debts-table">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>الاسم</th>
                            <th>المبلغ</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الأيام المتبقية</th>
                            <th>الحالة</th>
                            <th>الملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم الشهادات -->
        <section id="certificates" class="section">
            <div class="section-header">
                <h2>إدارة الشهادات</h2>
                <button id="add-certificate-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إنشاء شهادة
                </button>
            </div>
            <div class="certificates-grid">
                <div class="certificate-card">
                    <div class="certificate-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3>شهادة التركيب</h3>
                    <p>إنشاء شهادة تركيب نظام الغاز</p>
                    <button class="btn btn-primary" onclick="createCertificate('installation')">
                        إنشاء شهادة
                    </button>
                </div>
                <div class="certificate-card">
                    <div class="certificate-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>شهادة المراقبة</h3>
                    <p>إنشاء شهادة مراقبة دورية</p>
                    <button class="btn btn-primary" onclick="createCertificate('monitoring')">
                        إنشاء شهادة
                    </button>
                </div>
            </div>
        </section>

        <!-- قسم المخزون -->
        <section id="inventory" class="section">
            <div class="section-header">
                <h2>إدارة المخزون</h2>
                <button id="add-item-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة صنف
                </button>
            </div>
            <div class="inventory-stats">
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="low-stock-items">0</h3>
                        <p>أصناف منخفضة المخزون</p>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-items">0</h3>
                        <p>إجمالي الأصناف</p>
                    </div>
                </div>
            </div>
            <div class="table-container">
                <table id="inventory-table">
                    <thead>
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الفئة</th>
                            <th>الكمية</th>
                            <th>الحد الأدنى</th>
                            <th>سعر الشراء</th>
                            <th>سعر البيع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم التقارير -->
        <section id="reports" class="section">
            <div class="section-header">
                <h2>التقارير والإحصائيات</h2>
            </div>
            <div class="reports-grid">
                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>تقرير الزبائن</h3>
                    <p>تقرير شامل عن جميع الزبائن</p>
                    <button class="btn btn-primary" onclick="generateReport('customers')">
                        إنشاء التقرير
                    </button>
                </div>
                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3>تقرير البطاقات</h3>
                    <p>تقرير بطاقات الغاز والصلاحيات</p>
                    <button class="btn btn-primary" onclick="generateReport('cards')">
                        إنشاء التقرير
                    </button>
                </div>
                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <h3>تقرير المواعيد</h3>
                    <p>تقرير المواعيد والخدمات</p>
                    <button class="btn btn-primary" onclick="generateReport('appointments')">
                        إنشاء التقرير
                    </button>
                </div>
                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>تقرير الأداء</h3>
                    <p>إحصائيات الأداء الشهرية</p>
                    <button class="btn btn-primary" onclick="generateReport('performance')">
                        إنشاء التقرير
                    </button>
                </div>
            </div>
        </section>

        <!-- قسم الموظفين -->
        <section id="employees" class="section">
            <div class="section-header">
                <h2>إدارة الموظفين</h2>
                <div class="header-actions">
                    <div class="search-container">
                        <input type="text" id="employees-search" placeholder="البحث في الموظفين..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                    <button id="add-employee-btn" class="btn btn-primary" onclick="addEmployee()">
                        <i class="fas fa-user-plus"></i> إضافة موظف
                    </button>
                    <button id="attendance-report-btn" class="btn btn-info" onclick="showAttendanceReport()">
                        <i class="fas fa-clock"></i> تقرير الحضور
                    </button>
                    <button id="payroll-btn" class="btn btn-success" onclick="showPayroll()">
                        <i class="fas fa-money-bill-wave"></i> الرواتب
                    </button>
                </div>
            </div>

            <div class="employees-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-employees">0</h3>
                        <p>إجمالي الموظفين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-employees">0</h3>
                        <p>موظف نشط</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="present-today">0</h3>
                        <p>حاضر اليوم</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-payroll">0</h3>
                        <p>إجمالي الرواتب</p>
                    </div>
                </div>
            </div>

            <div class="table-container">
                <table id="employees-table" class="data-table">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>المنصب</th>
                            <th>القسم</th>
                            <th>معلومات الاتصال</th>
                            <th>تاريخ التوظيف</th>
                            <th>الخبرة</th>
                            <th>الراتب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- قسم الإعدادات -->
        <section id="settings" class="section">
            <div class="section-header">
                <h2>إعدادات النظام</h2>
            </div>
            <div class="settings-grid">
                <div class="settings-card">
                    <h3>معلومات المؤسسة</h3>
                    <form id="company-settings-form">
                        <div class="form-group">
                            <label for="company-name">اسم المؤسسة:</label>
                            <input type="text" id="company-name" value="مؤسسة وقود المستقبل">
                        </div>
                        <div class="form-group">
                            <label for="company-address">العنوان:</label>
                            <input type="text" id="company-address" placeholder="عنوان المؤسسة">
                        </div>
                        <div class="form-group">
                            <label for="company-phone">الهاتف:</label>
                            <input type="tel" id="company-phone" placeholder="رقم الهاتف">
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                        <button type="button" class="btn btn-secondary" onclick="showBackupsList()">
                            <i class="fas fa-database"></i> إدارة النسخ الاحتياطية
                        </button>
                    </form>
                </div>
                <div class="settings-card">
                    <h3>إعدادات النظام</h3>
                    <div class="setting-item">
                        <label class="switch">
                            <input type="checkbox" id="auto-backup">
                            <span class="slider"></span>
                        </label>
                        <span>النسخ الاحتياطي التلقائي</span>
                    </div>
                    <div class="setting-item">
                        <label class="switch">
                            <input type="checkbox" id="notifications-enabled" checked>
                            <span class="slider"></span>
                        </label>
                        <span>تفعيل الإشعارات</span>
                    </div>
                    <div class="setting-item">
                        <button class="btn btn-danger" onclick="clearAllData()">
                            <i class="fas fa-trash"></i> مسح جميع البيانات
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- النافذة المنبثقة -->
    <div id="modal-overlay" class="modal-overlay">
        <div id="modal-content" class="modal-content">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <!-- نافذة إضافة/تعديل عملية جدول الإرسال -->
    <div id="transmission-entry-modal" class="modal-overlay">
        <div class="modal-content">
            <h3 id="transmission-modal-title">إضافة عملية جديدة</h3>
            <form id="transmission-entry-form">
                <div class="form-group">
                    <label>نوع العملية:</label>
                    <select id="transmission-entry-type" required>
                        <option value="تركيب">تركيب</option>
                        <option value="مراقبة">مراقبة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>رقم خزان الغاز:</label>
                    <input type="text" id="transmission-entry-tank-number" required>
                </div>
                <div class="form-group">
                    <label>الصنف:</label>
                    <input type="text" id="transmission-entry-car-type" required>
                </div>
                <div class="form-group">
                    <label>الرقم التسلسلي:</label>
                    <input type="text" id="transmission-entry-serial-number" required>
                </div>
                <div class="form-group">
                    <label>رقم التسجيل:</label>
                    <input type="text" id="transmission-entry-registration-number" required>
                </div>
                <div class="form-group">
                    <label>الإسم واللقب:</label>
                    <input type="text" id="transmission-entry-owner-name" required>
                </div>
                <div class="form-group">
                    <label>تاريخ العملية:</label>
                    <input type="date" id="transmission-entry-operation-date" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeTransmissionModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نظام الإشعارات -->
    <div id="toast" class="toast">
        <span id="toast-message"></span>
    </div>

    <!-- حاوي التنبيهات الذكية -->
    <div id="smart-alerts-container" class="smart-alerts-container">
        <!-- سيتم ملؤها بواسطة JavaScript -->
    </div>

    <!-- لوحة الإشعارات -->
    <div id="notifications-panel" class="notifications-panel">
        <div class="notifications-header">
            <h3>الإشعارات</h3>
            <button id="close-notifications" class="btn-close">×</button>
        </div>
        <div id="notifications-list" class="notifications-list">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <!-- مكتبات التصدير -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>

    <script src="scripts/main.js"></script>
</body>
</html>
