// ==================== تسجيل الدخول للوحة التحكم في التراخيص ====================

// بيانات تسجيل الدخول
const loginCredentials = {
    admin: {
        username: 'admin',
        password: 'admin123',
        name: 'مدير النظام',
        role: 'admin',
        permissions: ['licenses', 'workshops', 'reports', 'settings']
    },
    developer: {
        key: 'DEV-2024-GASMANAGEMENT-MASTER',
        password: 'GasSystem@2024!',
        name: 'مطور النظام',
        role: 'developer',
        permissions: ['all']
    }
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل واجهة تسجيل الدخول...');
    
    // التحقق من وجود جلسة نشطة
    checkExistingSession();
    
    // إعداد معالجات النماذج
    setupFormHandlers();
    
    // تركيز على أول حقل
    focusFirstInput();
    
    console.log('✅ تم تحميل واجهة تسجيل الدخول بنجاح');
});

// التحقق من وجود جلسة نشطة
function checkExistingSession() {
    const adminSession = sessionStorage.getItem('adminSession');
    const developerSession = sessionStorage.getItem('developerSession');
    
    if (adminSession || developerSession) {
        showMessage('تم العثور على جلسة نشطة، جاري إعادة التوجيه...', 'success');
        setTimeout(() => {
            window.location.href = 'license-control-panel.html';
        }, 1500);
    }
}

// إعداد معالجات النماذج
function setupFormHandlers() {
    // نموذج تسجيل دخول المدير
    document.getElementById('admin-login-form').addEventListener('submit', function(e) {
        e.preventDefault();
        handleAdminLogin();
    });
    
    // نموذج تسجيل دخول المطور
    document.getElementById('developer-login-form').addEventListener('submit', function(e) {
        e.preventDefault();
        handleDeveloperLogin();
    });
    
    // معالجة الضغط على Enter
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const activeForm = document.querySelector('.login-form:not([style*="display: none"])');
            if (activeForm) {
                activeForm.dispatchEvent(new Event('submit'));
            }
        }
    });
}

// تبديل التبويبات
function switchTab(tabType) {
    // تحديث أزرار التبويب
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // إخفاء جميع النماذج
    document.querySelectorAll('.login-form').forEach(form => {
        form.style.display = 'none';
    });
    
    // إظهار النموذج المحدد
    const targetForm = document.getElementById(`${tabType}-login-form`);
    if (targetForm) {
        targetForm.style.display = 'block';
        focusFirstInput();
    }
    
    // مسح الرسائل
    clearMessages();
}

// تركيز على أول حقل إدخال
function focusFirstInput() {
    setTimeout(() => {
        const visibleForm = document.querySelector('.login-form:not([style*="display: none"])');
        if (visibleForm) {
            const firstInput = visibleForm.querySelector('input');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }, 100);
}

// معالجة تسجيل دخول المدير
function handleAdminLogin() {
    const username = document.getElementById('admin-username').value.trim();
    const password = document.getElementById('admin-password').value;
    const loginBtn = document.getElementById('admin-login-btn');
    
    // التحقق من البيانات
    if (!username || !password) {
        showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
        return;
    }
    
    // عرض مؤشر التحميل
    setLoadingState(loginBtn, true);
    
    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // التحقق من صحة البيانات
        if (username === loginCredentials.admin.username && 
            password === loginCredentials.admin.password) {
            
            // إنشاء جلسة المدير
            const adminSession = {
                id: 'admin-' + Date.now(),
                username: username,
                name: loginCredentials.admin.name,
                role: loginCredentials.admin.role,
                permissions: loginCredentials.admin.permissions,
                loginTime: new Date().toISOString(),
                isActive: true
            };
            
            // حفظ الجلسة
            sessionStorage.setItem('adminSession', JSON.stringify(adminSession));
            
            // عرض رسالة نجاح
            showMessage('تم تسجيل الدخول بنجاح! جاري إعادة التوجيه...', 'success');
            
            // إعادة التوجيه
            setTimeout(() => {
                window.location.href = 'license-control-panel.html';
            }, 1500);
            
        } else {
            showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            setLoadingState(loginBtn, false);
        }
    }, 1000);
}

// معالجة تسجيل دخول المطور
function handleDeveloperLogin() {
    const developerKey = document.getElementById('developer-key').value.trim();
    const password = document.getElementById('developer-password').value;
    const loginBtn = document.getElementById('developer-login-btn');
    
    // التحقق من البيانات
    if (!developerKey || !password) {
        showMessage('يرجى إدخال مفتاح المطور وكلمة المرور', 'error');
        return;
    }
    
    // عرض مؤشر التحميل
    setLoadingState(loginBtn, true);
    
    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // التحقق من صحة البيانات
        if (developerKey === loginCredentials.developer.key && 
            password === loginCredentials.developer.password) {
            
            // إنشاء جلسة المطور
            const developerSession = {
                id: 'dev-' + Date.now(),
                developerKey: developerKey,
                name: loginCredentials.developer.name,
                role: loginCredentials.developer.role,
                permissions: loginCredentials.developer.permissions,
                loginTime: new Date().toISOString(),
                isActive: true
            };
            
            // حفظ الجلسة
            sessionStorage.setItem('developerSession', JSON.stringify(developerSession));
            
            // عرض رسالة نجاح
            showMessage('تم تسجيل دخول المطور بنجاح! جاري إعادة التوجيه...', 'success');
            
            // إعادة التوجيه
            setTimeout(() => {
                window.location.href = 'license-control-panel.html';
            }, 1500);
            
        } else {
            showMessage('مفتاح المطور أو كلمة المرور غير صحيحة', 'error');
            setLoadingState(loginBtn, false);
        }
    }, 1000);
}

// تسجيل دخول سريع
function quickLogin(type) {
    if (type === 'admin') {
        // ملء بيانات المدير التجريبي
        document.getElementById('admin-username').value = loginCredentials.admin.username;
        document.getElementById('admin-password').value = loginCredentials.admin.password;
        
        // التبديل للتبويب المناسب
        switchTab('admin');
        
        // تسجيل الدخول تلقائياً
        setTimeout(() => {
            handleAdminLogin();
        }, 500);
        
    } else if (type === 'developer') {
        // ملء بيانات المطور التجريبي
        document.getElementById('developer-key').value = loginCredentials.developer.key;
        document.getElementById('developer-password').value = loginCredentials.developer.password;
        
        // التبديل للتبويب المناسب
        switchTab('developer');
        
        // تسجيل الدخول تلقائياً
        setTimeout(() => {
            handleDeveloperLogin();
        }, 500);
    }
}

// تبديل إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = event.target.querySelector('i') || event.target;
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// عرض رسالة
function showMessage(message, type) {
    clearMessages();
    
    const messageElement = document.getElementById(`${type}-message`);
    if (messageElement) {
        messageElement.textContent = message;
        messageElement.style.display = 'block';
        
        // إخفاء الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 5000);
    }
}

// مسح الرسائل
function clearMessages() {
    document.getElementById('error-message').style.display = 'none';
    document.getElementById('success-message').style.display = 'none';
}

// تعيين حالة التحميل للزر
function setLoadingState(button, isLoading) {
    if (isLoading) {
        button.disabled = true;
        const originalContent = button.innerHTML;
        button.setAttribute('data-original-content', originalContent);
        button.innerHTML = '<div class="loading"></div> جاري تسجيل الدخول...';
    } else {
        button.disabled = false;
        const originalContent = button.getAttribute('data-original-content');
        if (originalContent) {
            button.innerHTML = originalContent;
        }
    }
}

// عرض نافذة نسيان كلمة المرور
function showForgotPassword() {
    alert(`للحصول على كلمة مرور جديدة، يرجى التواصل مع الدعم الفني:

📞 الهاتف: +213 555 123 456
📧 البريد الإلكتروني: <EMAIL>
🕒 ساعات العمل: السبت - الخميس (8:00 - 17:00)

أو استخدم بيانات الوصول السريع للاختبار.`);
}

// عرض المساعدة
function showHelp() {
    alert(`مساعدة تسجيل الدخول:

👤 للمديرين:
- اسم المستخدم: admin
- كلمة المرور: admin123

👨‍💻 للمطورين:
- مفتاح المطور: DEV-2024-GASMANAGEMENT-MASTER
- كلمة مرور المطور: GasSystem@2024!

💡 نصائح:
- استخدم أزرار الوصول السريع للاختبار
- تأكد من اتصالك بالإنترنت
- تواصل مع الدعم الفني عند الحاجة`);
}

// عرض معلومات النظام
function showSystemInfo() {
    alert(`معلومات النظام:

🏷️ الاسم: نظام إدارة تراخيص محطات الغاز
📦 الإصدار: 2.0.0
🏗️ البناء: 2024.12.001
👨‍💻 المطور: فريق تطوير الأنظمة
📅 تاريخ الإصدار: ديسمبر 2024

🔧 الميزات:
- إدارة التراخيص والورشات
- نظام طلبات التفعيل
- تقارير وإحصائيات شاملة
- واجهة سهلة الاستخدام
- أمان متقدم

📞 الدعم الفني: +213 555 123 456`);
}

// تسجيل الأحداث للتحليل
function logEvent(eventType, details) {
    const event = {
        type: eventType,
        details: details,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };
    
    console.log('📊 حدث:', event);
    
    // يمكن إرسال البيانات لخادم التحليل هنا
}
