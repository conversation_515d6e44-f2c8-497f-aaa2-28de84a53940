# نظام إدارة مؤسسة وقود المستقبل - الإصدار المحسن

## 🆕 التحسينات الجديدة

### 🎨 تحسينات التصميم
- **نظام ألوان محسن وعصري** مع تدرجات لونية جديدة
- **تحسينات في الوضع المظلم** لراحة العين
- **أزرار محسنة** مع تأثيرات بصرية وتفاعلية
- **واجهة مستخدم أكثر حداثة** ومتجاوبة

### 📋 جدول الإرسال الجديد
- **نظام جدول إرسال مستقل ومتكامل**
- **ربط تلقائي** مع عمليات التركيب والمراقبة
- **إمكانية الطباعة والتصدير** إلى PDF
- **فلترة وبحث متقدم** في البيانات
- **واجهة منفصلة** لسهولة الاستخدام

### 🔗 النظام الذكي للعمليات (جديد)
- **نظام موحد للعمليات**: عند إضافة زبون جديد، يمكن اختيار نوع العملية المطلوبة
- **معالجة تلقائية ذكية**: النظام يعالج كل نوع عملية تلقائياً:
  - **تركيب**: إضافة إلى جدول الإرسال + إنشاء موعد + إشعار
  - **مراقبة**: إضافة إلى جدول الإرسال + تحديث بطاقة الغاز + موعد
  - **صيانة**: إنشاء موعد صيانة + إشعار
  - **تجديد**: تحديث/إنشاء بطاقة غاز + موعد
  - **فحص**: إنشاء موعد فحص + إشعار
- **ربط تلقائي شامل**: جميع الأنظمة مترابطة ومتزامنة
- **تتبع العمليات**: عرض آخر عملية مطلوبة في جدول الزبائن

### 📋 النظام الثنائي الذكي لجدول الإرسال (جديد)
- **إدارة تلقائية للفترات**: النظام يقسم السنة إلى فترتين (6 أشهر لكل فترة)
- **تذكيرات ذكية للطباعة**: إشعارات تلقائية قبل انتهاء كل فترة
- **أرشفة تلقائية**: حفظ الجداول المكتملة مع إمكانية الوصول إليها لاحقاً
- **منع الإضافة للجدول المنتهي**: فتح جدول جديد تلقائياً عند انتهاء الفترة
- **سجل شامل للطباعة**: تتبع جميع عمليات الطباعة مع التواريخ
- **واجهة ذكية محسنة**: عرض معلومات الفترة الحالية والأيام المتبقية

## ✨ الميزات الرئيسية

- ✅ **إدارة الزبائن والمركبات**
- ✅ **إدارة بطاقات الغاز والمواعيد**
- ✅ **إدارة المخزون والمبيعات والمشتريات**
- ✅ **إدارة الديون والمدفوعات**
- ✅ **إدارة الموردين**
- ✅ **نظام الشهادات** (تركيب ومراقبة دورية)
- ✅ **جدول الإرسال المتكامل** (جديد)
- ✅ **نظام الإشعارات والتذكيرات**
- ✅ **النسخ الاحتياطي التلقائي**
- ✅ **دعم الوضع المظلم المحسن**
- ✅ **واجهة مستخدم عربية متجاوبة**

## 📁 الملفات الجديدة

- `transmission-table.html` - جدول الإرسال المستقل
- تحسينات في `styles.css`
- وظائف جديدة في `script.js`

## 🛠️ التقنيات المستخدمة

- **HTML5, CSS3, JavaScript ES6+**
- **Electron** للتطبيق المكتبي
- **Chart.js** للرسوم البيانية
- **jsPDF** لتصدير PDF
- **Font Awesome** للأيقونات
- **CSS Grid & Flexbox** للتخطيط

## 🚀 كيفية التشغيل

1. **للنظام الرئيسي**: فتح ملف `index.html` في المتصفح
2. **لجدول الإرسال المستقل**: فتح ملف `transmission-table.html`
3. **للتطبيق المكتبي**: تشغيل عبر Electron

## 🤖 استخدام النظام الذكي للعمليات

### إضافة زبون جديد مع عملية:
1. **انتقل إلى قسم الزبائن** واضغط "إضافة زبون جديد"
2. **املأ البيانات الشخصية**: الاسم، الهاتف، العنوان، البريد الإلكتروني
3. **اختر نوع العملية المطلوبة**:
   - تركيب نظام الغاز
   - مراقبة دورية
   - صيانة وإصلاح
   - تجديد بطاقة
   - فحص فني
4. **حدد الأولوية**: عادية، مستعجلة، طارئة
5. **أضف ملاحظات العملية** (اختياري)
6. **املأ بيانات السيارة والخزان**
7. **احفظ البيانات**

### ما يحدث تلقائياً:
- ✅ **إنشاء ملف الزبون** مع جميع البيانات
- ✅ **معالجة العملية المطلوبة** حسب النوع:
  - **تركيب**: إضافة لجدول الإرسال + موعد تركيب + إشعار
  - **مراقبة**: إضافة لجدول الإرسال + تحديث بطاقة الغاز + موعد
  - **صيانة**: إنشاء موعد صيانة + إشعار
  - **تجديد**: تحديث/إنشاء بطاقة غاز + موعد تجديد
  - **فحص**: إنشاء موعد فحص + إشعار
- ✅ **تحديث جميع الجداول** المرتبطة
- ✅ **إشعارات تلقائية** للمتابعة

## 📋 استخدام النظام الثنائي الذكي لجدول الإرسال

### 🎯 **المفهوم الأساسي:**
النظام يقسم السنة إلى **فترتين ثنائيتين**:
- **الفترة الأولى**: يناير - يونيو (6 أشهر)
- **الفترة الثانية**: يوليو - ديسمبر (6 أشهر)

### 🚀 **الميزات الذكية:**

#### **1. إدارة تلقائية للفترات:**
- النظام يحدد الفترة الحالية تلقائياً
- عرض معلومات الفترة والأيام المتبقية
- تحديث تلقائي لحالة الفترة

#### **2. تذكيرات ذكية للطباعة:**
- إشعارات تلقائية قبل 7 أيام من انتهاء الفترة
- نوافذ تذكير مع خيارات سريعة للطباعة
- منع فقدان البيانات بسبب نسيان الطباعة

#### **3. أرشفة تلقائية:**
- حفظ تلقائي للجداول المكتملة
- إمكانية الوصول للجداول السابقة
- سجل شامل لجميع عمليات الطباعة

### 🎮 **كيفية الاستخدام:**

#### **الواجهة الجديدة:**
1. **معلومات الفترة الحالية**: عرض اسم الفترة والتواريخ والأيام المتبقية
2. **الأزرار الذكية**:
   - **طباعة الجدول الحالي**: طباعة فورية مع تسجيل العملية
   - **سجل الجداول**: عرض جميع الجداول المحفوظة
   - **أرشفة الجدول الحالي**: حفظ الجدول الحالي يدوياً
   - **إنشاء جدول جديد**: بدء فترة جديدة يدوياً

#### **العمليات التلقائية:**
- **إضافة العمليات**: تُضاف تلقائياً من النظام الذكي للعمليات
- **التحقق من الفترة**: فحص يومي لحالة الفترة
- **التذكيرات**: إشعارات تلقائية للطباعة
- **الأرشفة**: حفظ تلقائي عند انتهاء الفترة

#### **إدارة الجداول المحفوظة:**
1. **عرض السجل**: قائمة بجميع الجداول السابقة
2. **طباعة الجداول القديمة**: إعادة طباعة أي جدول محفوظ
3. **تصدير PDF**: تصدير الجداول المحفوظة
4. **تتبع الطباعة**: سجل مفصل لجميع عمليات الطباعة

### 🔄 **سير العمل التلقائي:**

1. **بداية الفترة**: النظام ينشئ جدول جديد تلقائياً
2. **خلال الفترة**: إضافة العمليات تلقائياً من النظام الذكي
3. **قبل النهاية**: تذكيرات ذكية للطباعة (7 أيام)
4. **نهاية الفترة**: أرشفة تلقائية وإنشاء جدول جديد
5. **الأرشيف**: حفظ دائم مع إمكانية الوصول والطباعة

### 📊 **المزايا:**
- ✅ **لا فقدان للبيانات**: أرشفة تلقائية شاملة
- ✅ **تذكيرات ذكية**: عدم نسيان مواعيد الطباعة
- ✅ **تنظيم مثالي**: فصل واضح بين الفترات
- ✅ **سهولة الوصول**: استرجاع سريع للجداول السابقة
- ✅ **تتبع شامل**: سجل مفصل لجميع العمليات

## 📋 استخدام جدول الإرسال التقليدي

### من النظام الرئيسي:
1. انتقل إلى قسم "جدول الإرسال"
2. العمليات تُضاف تلقائياً من النظام الذكي
3. يمكن إضافة عمليات يدوياً أيضاً
4. استخدم الفلاتر للبحث والتصفية
5. اطبع أو صدّر إلى PDF

### من الملف المستقل:
1. افتح `transmission-table.html`
2. البيانات متزامنة مع النظام الرئيسي
3. واجهة مخصصة لجدول الإرسال
4. إمكانيات طباعة محسنة

### الربط التلقائي الذكي:
- عند إضافة زبون جديد مع عملية تركيب أو مراقبة
- عند إصدار شهادة تركيب أو مراقبة
- يتم إضافة العملية تلقائياً لجدول الإرسال
- تجنب التكرار والأخطاء

## ⚙️ الإعدادات

- **تخصيص اسم المحل** وساعات العمل
- **إعداد تذكيرات** البطاقات والديون
- **إعداد النسخ الاحتياطي** إلى تيليجرام
- **تخصيص ألوان النظام**

## 🔒 ميزات الأمان

- **نسخ احتياطية تلقائية** منتظمة
- **حفظ محلي آمن** في المتصفح
- **تشفير البيانات الحساسة**
- **نظام استرداد البيانات**

## 🎯 نصائح الاستخدام

### اختصارات لوحة المفاتيح:
- `Ctrl + S` - حفظ البيانات
- `Ctrl + N` - إضافة زبون جديد
- `Ctrl + P` - طباعة
- `Ctrl + F` - البحث
- `Ctrl + B` - نسخة احتياطية
- `F1` - المساعدة

### أفضل الممارسات:
- قم بعمل نسخة احتياطية دورية
- استخدم الفلاتر لسهولة البحث
- تحقق من البيانات قبل الطباعة
- استخدم الوضع المظلم لراحة العين

## 📞 للدعم التقني

- راجع ملفات التوثيق في مجلد `docs/`
- تواصل مع فريق التطوير
- تحقق من التحديثات الجديدة

## 📝 ملاحظات الإصدار

**الإصدار: 2.3.0 - Enhanced Edition**
**تاريخ التحديث: ديسمبر 2024**

### التحسينات في هذا الإصدار:
- نظام ألوان محسن
- جدول إرسال متكامل
- ربط تلقائي للعمليات
- تحسينات في الأداء
- واجهة مستخدم محسنة

### إصلاحات الأخطاء:
- تحسين استقرار النظام
- إصلاح مشاكل الطباعة
- تحسين التوافق مع المتصفحات
- إصلاح مشاكل الحفظ

---

**حقوق الطبع والنشر © 2024 مؤسسة وقود المستقبل**
**جميع الحقوق محفوظة**
