// ==================== لوحة التحكم في التراخيص ====================

// بيانات النظام
let licenseSystem = {
    licenses: JSON.parse(localStorage.getItem('systemLicenses')) || [],
    workshops: JSON.parse(localStorage.getItem('systemWorkshops')) || [],
    requests: JSON.parse(localStorage.getItem('activationRequests')) || [],
    settings: JSON.parse(localStorage.getItem('licenseSettings')) || {
        licenseTypes: {
            trial: { name: 'تجريبي', duration: 30, price: 0, features: ['basic'] },
            professional: { name: 'احترافي', duration: 365, price: 5000, features: ['basic', 'advanced'] },
            enterprise: { name: 'مؤسسي', duration: 3650, price: 15000, features: ['basic', 'advanced', 'premium'] }
        },
        autoApproval: false,
        notificationsEnabled: true
    },
    currentUser: JSON.parse(sessionStorage.getItem('adminSession')) || {
        id: 'admin',
        name: 'مدير النظام',
        role: 'admin'
    }
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل لوحة التحكم في التراخيص...');
    
    // التحقق من صلاحية الوصول
    if (!checkAdminAccess()) {
        redirectToLogin();
        return;
    }
    
    // تحديث واجهة المستخدم
    updateUserInfo();
    updateDashboardStats();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(updateDashboardStats, 30000);
    
    console.log('✅ تم تحميل لوحة التحكم بنجاح');
});

// التحقق من صلاحية الوصول
function checkAdminAccess() {
    const adminSession = sessionStorage.getItem('adminSession');
    const developerSession = sessionStorage.getItem('developerSession');
    
    return adminSession || developerSession;
}

// إعادة التوجيه لتسجيل الدخول
function redirectToLogin() {
    window.location.href = 'license-login.html';
}

// تحديث معلومات المستخدم
function updateUserInfo() {
    const userElement = document.getElementById('current-user');
    if (userElement) {
        userElement.textContent = licenseSystem.currentUser.name;
    }
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    const now = new Date();
    
    // حساب التراخيص النشطة
    const activeLicenses = licenseSystem.licenses.filter(license => {
        const expiryDate = new Date(license.expiryDate);
        return license.status === 'active' && expiryDate > now;
    }).length;
    
    // حساب الطلبات المعلقة
    const pendingRequests = licenseSystem.requests.filter(request => 
        request.status === 'pending'
    ).length;
    
    // حساب التراخيص المنتهية
    const expiredLicenses = licenseSystem.licenses.filter(license => {
        const expiryDate = new Date(license.expiryDate);
        return expiryDate <= now || license.status === 'expired';
    }).length;
    
    // إجمالي الورشات
    const totalWorkshops = licenseSystem.workshops.length;
    
    // تحديث العناصر
    updateElement('active-licenses', activeLicenses);
    updateElement('pending-requests', pendingRequests);
    updateElement('expired-licenses', expiredLicenses);
    updateElement('total-workshops', totalWorkshops);
    
    // تحديث شارة الإشعارات
    updateNotificationBadge(pendingRequests);
}

// تحديث عنصر HTML
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
        
        // إضافة تأثير بصري للتحديث
        element.style.transform = 'scale(1.1)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 200);
    }
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const quickBells = document.querySelectorAll('.quick-btn i.fa-bell');
    quickBells.forEach(bell => {
        const parent = bell.parentElement;
        let badge = parent.querySelector('.notification-badge');
        
        if (count > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'notification-badge';
                badge.style.cssText = `
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    background: #ef4444;
                    color: white;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                `;
                parent.style.position = 'relative';
                parent.appendChild(badge);
            }
            badge.textContent = count;
        } else if (badge) {
            badge.remove();
        }
    });
}

// إنشاء ترخيص جديد
function showCreateLicense() {
    const modal = createModal('إنشاء ترخيص جديد', `
        <form id="create-license-form" class="license-form">
            <div class="form-group">
                <label for="workshop-name">اسم الورشة:</label>
                <input type="text" id="workshop-name" required placeholder="أدخل اسم الورشة">
            </div>
            
            <div class="form-group">
                <label for="owner-name">اسم المالك:</label>
                <input type="text" id="owner-name" required placeholder="أدخل اسم المالك">
            </div>
            
            <div class="form-group">
                <label for="phone-number">رقم الهاتف:</label>
                <input type="tel" id="phone-number" required placeholder="أدخل رقم الهاتف">
            </div>
            
            <div class="form-group">
                <label for="license-type">نوع الترخيص:</label>
                <select id="license-type" required onchange="updateLicenseInfo()">
                    <option value="">اختر نوع الترخيص</option>
                    <option value="trial">تجريبي (30 يوم - مجاني)</option>
                    <option value="professional">احترافي (سنة واحدة - 5000 دج)</option>
                    <option value="enterprise">مؤسسي (10 سنوات - 15000 دج)</option>
                </select>
            </div>
            
            <div id="license-info" class="license-info" style="display: none;">
                <div class="info-card">
                    <h4 id="license-type-name"></h4>
                    <p><strong>المدة:</strong> <span id="license-duration"></span></p>
                    <p><strong>السعر:</strong> <span id="license-price"></span></p>
                    <p><strong>الميزات:</strong> <span id="license-features"></span></p>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إنشاء الترخيص
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </form>
    `);
    
    // معالج النموذج
    document.getElementById('create-license-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createNewLicense();
    });
}

// تحديث معلومات الترخيص
function updateLicenseInfo() {
    const licenseType = document.getElementById('license-type').value;
    const infoDiv = document.getElementById('license-info');
    
    if (licenseType && licenseSystem.settings.licenseTypes[licenseType]) {
        const typeInfo = licenseSystem.settings.licenseTypes[licenseType];
        
        document.getElementById('license-type-name').textContent = typeInfo.name;
        document.getElementById('license-duration').textContent = 
            typeInfo.duration === 30 ? '30 يوم' : 
            typeInfo.duration === 365 ? 'سنة واحدة' : 
            '10 سنوات';
        document.getElementById('license-price').textContent = 
            typeInfo.price === 0 ? 'مجاني' : `${typeInfo.price} دج`;
        document.getElementById('license-features').textContent = 
            typeInfo.features.join(', ');
        
        infoDiv.style.display = 'block';
    } else {
        infoDiv.style.display = 'none';
    }
}

// إنشاء ترخيص جديد
function createNewLicense() {
    const formData = {
        workshopName: document.getElementById('workshop-name').value,
        ownerName: document.getElementById('owner-name').value,
        phoneNumber: document.getElementById('phone-number').value,
        licenseType: document.getElementById('license-type').value
    };
    
    // التحقق من البيانات
    if (!formData.workshopName || !formData.ownerName || !formData.phoneNumber || !formData.licenseType) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إنشاء الترخيص
    const license = {
        id: generateId(),
        workshopName: formData.workshopName,
        ownerName: formData.ownerName,
        phoneNumber: formData.phoneNumber,
        licenseType: formData.licenseType,
        activationCode: generateActivationCode(formData.licenseType),
        status: 'active',
        createdDate: new Date().toISOString(),
        expiryDate: calculateExpiryDate(formData.licenseType),
        features: licenseSystem.settings.licenseTypes[formData.licenseType].features
    };
    
    // إضافة الترخيص
    licenseSystem.licenses.push(license);
    
    // إنشاء الورشة إذا لم تكن موجودة
    const existingWorkshop = licenseSystem.workshops.find(w => 
        w.name === formData.workshopName && w.ownerName === formData.ownerName
    );
    
    if (!existingWorkshop) {
        const workshop = {
            id: generateId(),
            name: formData.workshopName,
            ownerName: formData.ownerName,
            phoneNumber: formData.phoneNumber,
            licenseId: license.id,
            status: 'active',
            createdDate: new Date().toISOString()
        };
        
        licenseSystem.workshops.push(workshop);
    }
    
    // حفظ البيانات
    saveData();
    
    // تحديث الإحصائيات
    updateDashboardStats();
    
    // إغلاق النافذة وإظهار رسالة نجاح
    closeModal();
    showNotification('تم إنشاء الترخيص بنجاح!', 'success');
    
    // عرض كود التفعيل
    showActivationCode(license);
}

// عرض كود التفعيل
function showActivationCode(license) {
    const modal = createModal('كود التفعيل', `
        <div class="activation-code-display">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>تم إنشاء الترخيص بنجاح!</h3>
            
            <div class="license-details">
                <div class="detail-item">
                    <span class="label">الورشة:</span>
                    <span class="value">${license.workshopName}</span>
                </div>
                <div class="detail-item">
                    <span class="label">المالك:</span>
                    <span class="value">${license.ownerName}</span>
                </div>
                <div class="detail-item">
                    <span class="label">نوع الترخيص:</span>
                    <span class="value">${licenseSystem.settings.licenseTypes[license.licenseType].name}</span>
                </div>
                <div class="detail-item">
                    <span class="label">تاريخ الانتهاء:</span>
                    <span class="value">${formatDate(license.expiryDate)}</span>
                </div>
            </div>
            
            <div class="activation-code">
                <label>كود التفعيل:</label>
                <div class="code-display">
                    <span id="activation-code">${license.activationCode}</span>
                    <button class="btn btn-secondary" onclick="copyActivationCode()">
                        <i class="fas fa-copy"></i>
                        نسخ
                    </button>
                </div>
            </div>
            
            <div class="instructions">
                <h4>تعليمات للعميل:</h4>
                <ol>
                    <li>افتح نظام إدارة محطة الغاز</li>
                    <li>اضغط على "طلب تفعيل النظام"</li>
                    <li>أدخل كود التفعيل: <strong>${license.activationCode}</strong></li>
                    <li>اضغط على "تفعيل النظام"</li>
                </ol>
            </div>
            
            <div class="form-actions">
                <button class="btn btn-primary" onclick="closeModal()">
                    <i class="fas fa-check"></i>
                    تم
                </button>
                <button class="btn btn-secondary" onclick="sendActivationCode('${license.id}')">
                    <i class="fas fa-paper-plane"></i>
                    إرسال الكود
                </button>
            </div>
        </div>
    `);
}

// نسخ كود التفعيل
function copyActivationCode() {
    const codeElement = document.getElementById('activation-code');
    const code = codeElement.textContent;
    
    navigator.clipboard.writeText(code).then(() => {
        showNotification('تم نسخ كود التفعيل', 'success');
    }).catch(() => {
        // طريقة بديلة للنسخ
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('تم نسخ كود التفعيل', 'success');
    });
}

// دوال مساعدة
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateActivationCode(licenseType) {
    const prefix = licenseType.toUpperCase().substr(0, 3);
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `ACT-${prefix}-${timestamp}-${random}`;
}

function calculateExpiryDate(licenseType) {
    const duration = licenseSystem.settings.licenseTypes[licenseType].duration;
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + duration);
    return expiryDate.toISOString();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

function saveData() {
    localStorage.setItem('systemLicenses', JSON.stringify(licenseSystem.licenses));
    localStorage.setItem('systemWorkshops', JSON.stringify(licenseSystem.workshops));
    localStorage.setItem('activationRequests', JSON.stringify(licenseSystem.requests));
    localStorage.setItem('licenseSettings', JSON.stringify(licenseSystem.settings));
}

// عرض الطلبات المعلقة
function showPendingRequests() {
    const pendingRequests = licenseSystem.requests.filter(request =>
        request.status === 'pending'
    );

    let requestsHTML = `
        <div class="requests-container">
            <h3><i class="fas fa-list-alt"></i> الطلبات المعلقة (${pendingRequests.length})</h3>
    `;

    if (pendingRequests.length === 0) {
        requestsHTML += `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h4>لا توجد طلبات معلقة</h4>
                <p>جميع الطلبات تم معالجتها</p>
            </div>
        `;
    } else {
        requestsHTML += '<div class="requests-list">';

        pendingRequests.forEach(request => {
            requestsHTML += `
                <div class="request-card">
                    <div class="request-header">
                        <h4>${request.workshopName}</h4>
                        <span class="request-date">${formatDate(request.timestamp)}</span>
                    </div>
                    <div class="request-details">
                        <div class="detail-row">
                            <span class="label">المالك:</span>
                            <span class="value">${request.ownerName}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">الهاتف:</span>
                            <span class="value">${request.phoneNumber}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">نوع الترخيص:</span>
                            <span class="value">${licenseSystem.settings.licenseTypes[request.licenseType]?.name || request.licenseType}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">رقم الطلب:</span>
                            <span class="value">${request.requestNumber}</span>
                        </div>
                    </div>
                    <div class="request-actions">
                        <button class="btn btn-success" onclick="approveRequest('${request.id}')">
                            <i class="fas fa-check"></i>
                            موافقة
                        </button>
                        <button class="btn btn-danger" onclick="rejectRequest('${request.id}')">
                            <i class="fas fa-times"></i>
                            رفض
                        </button>
                        <button class="btn btn-secondary" onclick="viewRequestDetails('${request.id}')">
                            <i class="fas fa-eye"></i>
                            تفاصيل
                        </button>
                    </div>
                </div>
            `;
        });

        requestsHTML += '</div>';
    }

    requestsHTML += '</div>';

    createModal('الطلبات المعلقة', requestsHTML);
}

// الموافقة على طلب
function approveRequest(requestId) {
    const request = licenseSystem.requests.find(r => r.id === requestId);
    if (!request) return;

    if (confirm(`هل أنت متأكد من الموافقة على طلب "${request.workshopName}"؟`)) {
        // تحديث حالة الطلب
        request.status = 'approved';
        request.approvedDate = new Date().toISOString();
        request.approvedBy = licenseSystem.currentUser.name;

        // إنشاء ترخيص جديد
        const license = {
            id: generateId(),
            workshopName: request.workshopName,
            ownerName: request.ownerName,
            phoneNumber: request.phoneNumber,
            licenseType: request.licenseType,
            activationCode: generateActivationCode(request.licenseType),
            status: 'active',
            createdDate: new Date().toISOString(),
            expiryDate: calculateExpiryDate(request.licenseType),
            features: licenseSystem.settings.licenseTypes[request.licenseType].features,
            requestId: requestId
        };

        licenseSystem.licenses.push(license);

        // إنشاء ورشة جديدة
        const workshop = {
            id: generateId(),
            name: request.workshopName,
            ownerName: request.ownerName,
            phoneNumber: request.phoneNumber,
            licenseId: license.id,
            status: 'active',
            createdDate: new Date().toISOString()
        };

        licenseSystem.workshops.push(workshop);

        // حفظ البيانات
        saveData();

        // تحديث الإحصائيات
        updateDashboardStats();

        // إظهار كود التفعيل
        showActivationCode(license);

        // تحديث قائمة الطلبات
        showPendingRequests();

        showNotification('تم الموافقة على الطلب وإنشاء الترخيص بنجاح!', 'success');
    }
}

// رفض طلب
function rejectRequest(requestId) {
    const request = licenseSystem.requests.find(r => r.id === requestId);
    if (!request) return;

    const reason = prompt('سبب الرفض (اختياري):');

    if (confirm(`هل أنت متأكد من رفض طلب "${request.workshopName}"؟`)) {
        // تحديث حالة الطلب
        request.status = 'rejected';
        request.rejectedDate = new Date().toISOString();
        request.rejectedBy = licenseSystem.currentUser.name;
        request.rejectionReason = reason || 'لم يتم تحديد السبب';

        // حفظ البيانات
        saveData();

        // تحديث الإحصائيات
        updateDashboardStats();

        // تحديث قائمة الطلبات
        showPendingRequests();

        showNotification('تم رفض الطلب', 'warning');
    }
}

// إدارة التراخيص
function showLicenseManager() {
    let licensesHTML = `
        <div class="license-manager">
            <div class="manager-header">
                <h3><i class="fas fa-cogs"></i> إدارة التراخيص</h3>
                <div class="manager-actions">
                    <button class="btn btn-primary" onclick="showCreateLicense()">
                        <i class="fas fa-plus"></i>
                        ترخيص جديد
                    </button>
                    <button class="btn btn-secondary" onclick="exportLicenses()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="licenses-table">
                <table>
                    <thead>
                        <tr>
                            <th>الورشة</th>
                            <th>المالك</th>
                            <th>نوع الترخيص</th>
                            <th>تاريخ الإنشاء</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    licenseSystem.licenses.forEach(license => {
        const isExpired = new Date(license.expiryDate) <= new Date();
        const statusClass = isExpired ? 'status-expired' :
                           license.status === 'active' ? 'status-active' : 'status-inactive';
        const statusText = isExpired ? 'منتهي' :
                          license.status === 'active' ? 'نشط' : 'غير نشط';

        licensesHTML += `
            <tr>
                <td>${license.workshopName}</td>
                <td>${license.ownerName}</td>
                <td>${licenseSystem.settings.licenseTypes[license.licenseType]?.name || license.licenseType}</td>
                <td>${formatDate(license.createdDate)}</td>
                <td>${formatDate(license.expiryDate)}</td>
                <td><span class="${statusClass}">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="viewLicense('${license.id}')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="renewLicense('${license.id}')" title="تجديد">
                            <i class="fas fa-sync"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteLicense('${license.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    licensesHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    createModal('إدارة التراخيص', licensesHTML, 'large');
}

// إدارة الورشات
function showWorkshopManager() {
    createModal('إدارة الورشات', `
        <div class="workshop-manager">
            <h3><i class="fas fa-building"></i> إدارة الورشات</h3>
            <p>قريباً... سيتم إضافة إدارة شاملة للورشات</p>
        </div>
    `);
}

// التقارير
function showReports() {
    createModal('التقارير والإحصائيات', `
        <div class="reports-section">
            <h3><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h3>
            <p>قريباً... سيتم إضافة تقارير مفصلة</p>
        </div>
    `);
}

// الإعدادات
function showSettings() {
    createModal('إعدادات النظام', `
        <div class="settings-section">
            <h3><i class="fas fa-cog"></i> إعدادات النظام</h3>
            <p>قريباً... سيتم إضافة إعدادات شاملة</p>
        </div>
    `);
}

// المساعدة
function showHelp() {
    createModal('المساعدة', `
        <div class="help-section">
            <h3><i class="fas fa-question"></i> المساعدة</h3>
            <div class="help-content">
                <h4>كيفية استخدام لوحة التحكم:</h4>
                <ol>
                    <li>إنشاء تراخيص جديدة للورشات</li>
                    <li>مراجعة والموافقة على طلبات التفعيل</li>
                    <li>إدارة التراخيص الموجودة</li>
                    <li>عرض التقارير والإحصائيات</li>
                </ol>

                <h4>معلومات الاتصال:</h4>
                <p>📞 الهاتف: +213 555 123 456</p>
                <p>📧 البريد: <EMAIL></p>
            </div>
        </div>
    `);
}

// إنشاء نافذة منبثقة
function createModal(title, content, size = 'medium') {
    // إزالة النافذة الموجودة إن وجدت
    const existingModal = document.getElementById('modal-overlay');
    if (existingModal) {
        existingModal.remove();
    }

    // إنشاء النافذة الجديدة
    const modal = document.createElement('div');
    modal.id = 'modal-overlay';
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content ${size}">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إضافة الأنماط المطلوبة
    addModalStyles();

    // إظهار النافذة
    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// إغلاق النافذة المنبثقة
function closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// إضافة أنماط النافذة المنبثقة
function addModalStyles() {
    if (document.getElementById('modal-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'modal-styles';
    styles.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 2rem;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            max-width: 600px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .modal-content.large {
            max-width: 1000px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h3 {
            color: var(--text-color);
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
        }

        .close-btn:hover {
            opacity: 1;
        }

        .modal-body {
            padding: 1.5rem;
        }
    `;

    document.head.appendChild(styles);
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' :
                          type === 'error' ? 'exclamation-circle' :
                          type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: ${type === 'success' ? 'var(--success-color)' :
                     type === 'error' ? 'var(--danger-color)' :
                     type === 'warning' ? 'var(--warning-color)' : 'var(--info-color)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        z-index: 2000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: slideIn 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        sessionStorage.removeItem('adminSession');
        sessionStorage.removeItem('developerSession');
        window.location.href = 'license-login.html';
    }
}
