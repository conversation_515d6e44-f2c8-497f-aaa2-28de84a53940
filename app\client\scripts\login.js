// ==================== نظام تسجيل الدخول - نسخة العميل المحسنة ====================

// إعدادات النظام
const systemConfig = {
    version: '2.0.0',
    buildNumber: '2024.12.001',
    clientType: 'workshop',
    supportContact: {
        phone: '+213 555 123 456',
        email: '<EMAIL>',
        workingHours: 'السبت - الخميس (8:00 - 17:00)'
    },
    features: {
        customers: true,
        gasCards: true,
        appointments: true,
        transmission: true,
        inventory: true,
        reports: true,
        backup: true,
        notifications: true
    }
};

// بيانات النظام
let systemData = {
    workshops: JSON.parse(localStorage.getItem('clientWorkshops')) || [
        {
            id: 'demo-001',
            code: 'DEMO-001',
            name: 'الورشة التجريبية - وقود المستقبل',
            owner: 'مدير النظام',
            phone: '+213 555 123 456',
            address: 'الجزائر العاصمة - حي النصر',
            email: '<EMAIL>',
            licenseType: 'trial',
            expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            isActive: true,
            features: ['customers', 'cards', 'appointments', 'transmission'],
            users: [
                {
                    id: 'user-001',
                    username: 'admin',
                    password: 'admin123',
                    role: 'admin',
                    permissions: ['customers', 'cards', 'appointments', 'transmission'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                },
                {
                    id: 'user-002',
                    username: 'user',
                    password: 'user123',
                    role: 'user',
                    permissions: ['customers', 'cards'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }
            ],
            createdAt: new Date().toISOString(),
            lastLogin: null,
            activationCode: 'ACT-TRIAL-DEMO001',
            licenseKey: 'DEMO-TRIAL-2024-001'
        }
    ],
    currentSession: null,
    systemStats: {
        totalLogins: 0,
        lastLoginTime: null,
        activationRequests: 0
    }
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل نظام تسجيل الدخول - نسخة العميل المحسنة v' + systemConfig.version);

    // عرض مؤشر التحميل
    showGlobalLoading('جاري تهيئة النظام...');

    // تهيئة النظام مع تأخير للتأثير البصري
    setTimeout(() => {
        initializeSystem();
        setupLoginForm();
        checkExistingSession();
        updateBuildInfo();
        hideGlobalLoading();

        console.log('✅ تم تحميل النظام بنجاح');
        showToast('مرحباً بك في نظام إدارة محطة الغاز', 'success');
    }, 1000);
});

// تهيئة النظام
function initializeSystem() {
    // تحديث إحصائيات النظام
    updateSystemStats();

    // إعداد الأحداث
    setupEventListeners();

    // فحص التراخيص المنتهية
    checkExpiredLicenses();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // زر إظهار/إخفاء كلمة المرور
    const togglePassword = document.querySelector('.toggle-password');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }

    // إغلاق النوافذ المنبثقة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            hideModal();
        }
    });

    // اختصارات لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideModal();
        }
    });
}

// إعداد نموذج تسجيل الدخول
function setupLoginForm() {
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
}

// معالجة تسجيل الدخول
function handleLogin(e) {
    e.preventDefault();

    const workshopCode = document.getElementById('workshop-code').value.trim().toUpperCase();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember-me').checked;

    // التحقق من الحقول
    if (!workshopCode || !username || !password) {
        showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // عرض مؤشر التحميل
    showLoadingState(true);

    // محاكاة تأخير الشبكة
    setTimeout(() => {
        performLogin(workshopCode, username, password, rememberMe);
        showLoadingState(false);
    }, 1500);
}

// تنفيذ عملية تسجيل الدخول
function performLogin(workshopCode, username, password, rememberMe) {
    // البحث عن الورشة
    const workshop = systemData.workshops.find(w => w.code === workshopCode);

    if (!workshop) {
        showToast('كود الورشة غير صحيح', 'error');
        return;
    }

    // التحقق من حالة الورشة
    if (!workshop.isActive) {
        showToast('الورشة معطلة. يرجى التواصل مع الدعم الفني', 'error');
        showContactInfo();
        return;
    }

    // التحقق من صلاحية الترخيص
    if (new Date(workshop.expiryDate) < new Date()) {
        showToast('انتهت صلاحية ترخيص الورشة. يرجى التواصل مع الدعم الفني', 'error');
        showLicenseExpiredInfo(workshop);
        return;
    }

    // البحث عن المستخدم
    const user = workshop.users.find(u => u.username === username && u.password === password);

    if (!user) {
        showToast('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
        return;
    }

    if (!user.isActive) {
        showToast('المستخدم معطل. يرجى التواصل مع مدير النظام', 'error');
        return;
    }

    // إنشاء جلسة ناجحة
    createUserSession(workshop, user, rememberMe);
}

// إنشاء جلسة المستخدم
function createUserSession(workshop, user, rememberMe) {
    const session = {
        workshopId: workshop.id,
        workshopCode: workshop.code,
        workshopName: workshop.name,
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions || [],
        licenseType: workshop.licenseType,
        loginTime: new Date().toISOString(),
        expiryDate: workshop.expiryDate
    };

    // حفظ الجلسة
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem('gasSystemSession', JSON.stringify(session));

    // تحديث آخر تسجيل دخول
    workshop.lastLogin = new Date().toISOString();
    saveSystemData();

    // عرض رسالة نجاح
    showToast(`مرحباً ${user.username}! تم تسجيل الدخول بنجاح`, 'success');

    // عرض معلومات الجلسة
    showLoginSuccess(workshop, user);

    // إعادة التوجيه للنظام الرئيسي
    setTimeout(() => {
        window.location.href = 'main.html';
    }, 2000);
}

// عرض نجاح تسجيل الدخول
function showLoginSuccess(workshop, user) {
    const successHTML = `
        <div class="login-success">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>تم تسجيل الدخول بنجاح!</h3>
            <div class="session-info">
                <div class="info-item">
                    <span class="label">الورشة:</span>
                    <span class="value">${workshop.name}</span>
                </div>
                <div class="info-item">
                    <span class="label">المستخدم:</span>
                    <span class="value">${user.username}</span>
                </div>
                <div class="info-item">
                    <span class="label">الدور:</span>
                    <span class="value">${getRoleText(user.role)}</span>
                </div>
                <div class="info-item">
                    <span class="label">نوع الترخيص:</span>
                    <span class="value">${getLicenseTypeText(workshop.licenseType)}</span>
                </div>
                <div class="info-item">
                    <span class="label">صالح حتى:</span>
                    <span class="value">${formatDate(workshop.expiryDate)}</span>
                </div>
            </div>
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>جاري تحميل النظام الرئيسي...</p>
            </div>
        </div>
    `;

    showModal('تسجيل دخول ناجح', successHTML);
}

// التحقق من وجود جلسة
function checkExistingSession() {
    const savedSession = localStorage.getItem('gasSystemSession') || sessionStorage.getItem('gasSystemSession');

    if (savedSession) {
        try {
            const session = JSON.parse(savedSession);
            const workshop = systemData.workshops.find(w => w.id === session.workshopId);

            if (workshop && workshop.isActive && new Date(workshop.expiryDate) > new Date()) {
                // جلسة صالحة - عرض خيار المتابعة
                showExistingSessionOption(session, workshop);
                return;
            }
        } catch (error) {
            console.error('خطأ في قراءة الجلسة:', error);
        }

        // مسح الجلسة غير الصالحة
        clearInvalidSession();
    }
}

// عرض خيار الجلسة الموجودة
function showExistingSessionOption(session, workshop) {
    const sessionHTML = `
        <div class="existing-session">
            <h3><i class="fas fa-user-check"></i> جلسة موجودة</h3>
            <p>تم العثور على جلسة سابقة للورشة:</p>
            <div class="session-details">
                <div><strong>الورشة:</strong> ${workshop.name}</div>
                <div><strong>المستخدم:</strong> ${session.username}</div>
                <div><strong>آخر دخول:</strong> ${formatDateTime(session.loginTime)}</div>
            </div>
            <div class="session-actions">
                <button class="btn btn-primary" onclick="continueExistingSession()">
                    <i class="fas fa-play"></i>
                    متابعة الجلسة
                </button>
                <button class="btn btn-secondary" onclick="startNewSession()">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل دخول جديد
                </button>
            </div>
        </div>
    `;

    showModal('جلسة موجودة', sessionHTML);
}

// متابعة الجلسة الموجودة
function continueExistingSession() {
    hideModal();
    showToast('جاري تحميل النظام...', 'info');
    setTimeout(() => {
        window.location.href = 'main.html';
    }, 1000);
}

// بدء جلسة جديدة
function startNewSession() {
    clearInvalidSession();
    hideModal();
    showToast('يمكنك الآن تسجيل الدخول بحساب جديد', 'info');
}

// مسح الجلسة غير الصالحة
function clearInvalidSession() {
    localStorage.removeItem('gasSystemSession');
    sessionStorage.removeItem('gasSystemSession');
}

// طلب تفعيل جديد
function requestActivation() {
    const activationHTML = `
        <div class="activation-request">
            <h3><i class="fas fa-key"></i> طلب تفعيل النظام</h3>
            <p>املأ النموذج أدناه لطلب تفعيل النظام من المطور</p>

            <form id="activation-form">
                <div class="form-group">
                    <label for="workshop-name">اسم الورشة: *</label>
                    <input type="text" id="workshop-name" required placeholder="أدخل اسم الورشة">
                </div>

                <div class="form-group">
                    <label for="owner-name">اسم المالك: *</label>
                    <input type="text" id="owner-name" required placeholder="أدخل اسم مالك الورشة">
                </div>

                <div class="form-group">
                    <label for="phone-number">رقم الهاتف: *</label>
                    <input type="tel" id="phone-number" required placeholder="0555123456">
                </div>

                <div class="form-group">
                    <label for="email-address">البريد الإلكتروني:</label>
                    <input type="email" id="email-address" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="workshop-address">عنوان الورشة:</label>
                    <input type="text" id="workshop-address" placeholder="العنوان التفصيلي للورشة">
                </div>

                <div class="form-group">
                    <label for="license-type">نوع الترخيص المطلوب:</label>
                    <select id="license-type">
                        <option value="trial">تجريبي (30 يوم) - مجاني</option>
                        <option value="professional">احترافي (سنة واحدة)</option>
                        <option value="enterprise">مؤسسي (بدون انتهاء)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="activation-code">كود التفعيل (إذا كان متوفراً):</label>
                    <input type="text" id="activation-code" placeholder="ACT-XXXX-XXXX-XXXX">
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات إضافية:</label>
                    <textarea id="notes" rows="3" placeholder="أي ملاحظات أو متطلبات خاصة..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        إرسال طلب التفعيل
                    </button>
                    <button type="button" class="btn btn-info" onclick="tryActivationCode()">
                        <i class="fas fa-key"></i>
                        تجربة كود التفعيل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;

    showModal('طلب تفعيل النظام', activationHTML);

    // إعداد معالج النموذج
    document.getElementById('activation-form').addEventListener('submit', handleActivationRequest);
}

// معالجة طلب التفعيل
function handleActivationRequest(e) {
    e.preventDefault();

    const formData = {
        workshopName: document.getElementById('workshop-name').value.trim(),
        ownerName: document.getElementById('owner-name').value.trim(),
        phoneNumber: document.getElementById('phone-number').value.trim(),
        emailAddress: document.getElementById('email-address').value.trim(),
        workshopAddress: document.getElementById('workshop-address').value.trim(),
        licenseType: document.getElementById('license-type').value,
        activationCode: document.getElementById('activation-code').value.trim(),
        notes: document.getElementById('notes').value.trim()
    };

    // التحقق من الحقول المطلوبة
    if (!formData.workshopName || !formData.ownerName || !formData.phoneNumber) {
        showToast('يرجى ملء الحقول المطلوبة', 'error');
        return;
    }

    // محاكاة إرسال الطلب
    simulateActivationRequest(formData);
}

// محاكاة إرسال طلب التفعيل
function simulateActivationRequest(formData) {
    showToast('جاري إرسال طلب التفعيل...', 'info');

    // محاكاة تأخير الشبكة
    setTimeout(() => {
        // حفظ الطلب محلياً
        const request = {
            id: generateId(),
            ...formData,
            status: 'pending',
            timestamp: new Date().toISOString(),
            requestNumber: 'REQ-' + Date.now().toString(36).toUpperCase()
        };

        // حفظ في التخزين المحلي
        const savedRequests = JSON.parse(localStorage.getItem('activationRequests')) || [];
        savedRequests.push(request);
        localStorage.setItem('activationRequests', JSON.stringify(savedRequests));

        hideModal();
        showToast('تم إرسال طلب التفعيل بنجاح!', 'success');

        // عرض معلومات المتابعة
        showRequestConfirmation(request);

    }, 2000);
}

// عرض تأكيد الطلب
function showRequestConfirmation(request) {
    const confirmationHTML = `
        <div class="request-confirmation">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>تم إرسال طلب التفعيل بنجاح!</h3>

            <div class="request-details">
                <h4>تفاصيل الطلب:</h4>
                <div class="detail-item">
                    <span class="label">رقم الطلب:</span>
                    <span class="value">${request.requestNumber}</span>
                </div>
                <div class="detail-item">
                    <span class="label">اسم الورشة:</span>
                    <span class="value">${request.workshopName}</span>
                </div>
                <div class="detail-item">
                    <span class="label">نوع الترخيص:</span>
                    <span class="value">${getLicenseTypeText(request.licenseType)}</span>
                </div>
                <div class="detail-item">
                    <span class="label">وقت الإرسال:</span>
                    <span class="value">${formatDateTime(request.timestamp)}</span>
                </div>
            </div>

            <div class="next-steps">
                <h4>الخطوات التالية:</h4>
                <ol>
                    <li>سيقوم المطور بمراجعة طلبك خلال 24 ساعة</li>
                    <li>ستتلقى كود التفعيل عبر الهاتف أو البريد الإلكتروني</li>
                    <li>استخدم كود التفعيل لتفعيل النظام</li>
                    <li>ابدأ في استخدام النظام فوراً</li>
                </ol>
            </div>

            <div class="contact-reminder">
                <h4>معلومات التواصل:</h4>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>+213 555 123 456</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <span>ساعات العمل: السبت - الخميس (8:00 - 17:00)</span>
                </div>
            </div>

            <div class="confirmation-actions">
                <button class="btn btn-primary" onclick="checkRequestStatus('${request.id}')">
                    <i class="fas fa-search"></i>
                    تحقق من حالة الطلب
                </button>
                <button class="btn btn-info" onclick="requestActivation()">
                    <i class="fas fa-plus"></i>
                    طلب جديد
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('تأكيد إرسال الطلب', confirmationHTML);
}

// تجربة كود التفعيل
function tryActivationCode() {
    const activationCode = document.getElementById('activation-code').value.trim().toUpperCase();

    if (!activationCode) {
        showToast('يرجى إدخال كود التفعيل', 'error');
        return;
    }

    showToast('جاري التحقق من كود التفعيل...', 'info');

    // محاكاة التحقق من كود التفعيل
    setTimeout(() => {
        validateActivationCode(activationCode);
    }, 2000);
}

// التحقق من صحة كود التفعيل
function validateActivationCode(code) {
    // أكواد التفعيل الصالحة (للاختبار)
    const validCodes = [
        'ACT-TRIAL-123456',
        'ACT-PROFESSIONAL-789012',
        'ACT-ENTERPRISE-345678'
    ];

    // التحقق من تنسيق الكود
    const codePattern = /^ACT-[A-Z]+-[A-Z0-9]+$/;

    if (validCodes.includes(code) || codePattern.test(code)) {
        // كود صالح
        const licenseType = determineLicenseType(code);
        const workshopName = document.getElementById('workshop-name').value.trim() || 'ورشة جديدة';
        const ownerName = document.getElementById('owner-name').value.trim() || 'مالك الورشة';

        createWorkshopFromActivation(code, licenseType, workshopName, ownerName);

    } else {
        showToast('كود التفعيل غير صالح أو منتهي الصلاحية', 'error');
        showInvalidCodeHelp();
    }
}

// تحديد نوع الترخيص من الكود
function determineLicenseType(code) {
    if (code.includes('TRIAL')) return 'trial';
    if (code.includes('PROFESSIONAL')) return 'professional';
    if (code.includes('ENTERPRISE')) return 'enterprise';

    // افتراضي للأكواد الجديدة
    return 'trial';
}

// إنشاء ورشة من كود التفعيل
function createWorkshopFromActivation(activationCode, licenseType, workshopName, ownerName) {
    const workshop = {
        id: generateId(),
        code: generateWorkshopCode(),
        name: workshopName,
        owner: ownerName,
        phone: document.getElementById('phone-number')?.value || '',
        address: document.getElementById('workshop-address')?.value || '',
        email: document.getElementById('email-address')?.value || '',
        licenseType: licenseType,
        licenseKey: generateLicenseKey(),
        activationCode: activationCode,
        expiryDate: calculateExpiryDate(licenseType),
        isActive: true,
        users: [
            {
                id: generateId(),
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                permissions: getPermissionsByLicenseType(licenseType),
                isActive: true,
                createdAt: new Date().toISOString()
            }
        ],
        createdAt: new Date().toISOString(),
        lastLogin: null
    };

    // إضافة الورشة للنظام
    systemData.workshops.push(workshop);
    saveSystemData();

    hideModal();
    showToast('تم تفعيل النظام بنجاح!', 'success');

    // عرض معلومات الورشة الجديدة
    showWorkshopCreated(workshop);
}

// عرض معلومات الورشة المنشأة
function showWorkshopCreated(workshop) {
    const workshopHTML = `
        <div class="workshop-created">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>تم تفعيل النظام بنجاح!</h3>

            <div class="workshop-info">
                <h4>معلومات الورشة:</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">كود الورشة:</span>
                        <span class="value workshop-code">${workshop.code}</span>
                        <button class="copy-btn" onclick="copyToClipboard('${workshop.code}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="info-item">
                        <span class="label">اسم الورشة:</span>
                        <span class="value">${workshop.name}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">نوع الترخيص:</span>
                        <span class="value">${getLicenseTypeText(workshop.licenseType)}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">تاريخ الانتهاء:</span>
                        <span class="value">${formatDate(workshop.expiryDate)}</span>
                    </div>
                </div>
            </div>

            <div class="login-credentials">
                <h4>بيانات تسجيل الدخول:</h4>
                <div class="credentials-box">
                    <div class="credential-row">
                        <span class="label">كود الورشة:</span>
                        <code class="credential-value">${workshop.code}</code>
                        <button class="copy-btn" onclick="copyToClipboard('${workshop.code}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="credential-row">
                        <span class="label">اسم المستخدم:</span>
                        <code class="credential-value">admin</code>
                        <button class="copy-btn" onclick="copyToClipboard('admin')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="credential-row">
                        <span class="label">كلمة المرور:</span>
                        <code class="credential-value">admin123</code>
                        <button class="copy-btn" onclick="copyToClipboard('admin123')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="security-note">
                    <i class="fas fa-shield-alt"></i>
                    <span>احفظ هذه المعلومات في مكان آمن</span>
                </div>
            </div>

            <div class="workshop-actions">
                <button class="btn btn-success" onclick="loginWithNewWorkshop('${workshop.code}')">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول الآن
                </button>
                <button class="btn btn-info" onclick="copyAllCredentials('${workshop.code}')">
                    <i class="fas fa-copy"></i>
                    نسخ جميع المعلومات
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('تفعيل ناجح', workshopHTML);
}

// تسجيل دخول بالورشة الجديدة
function loginWithNewWorkshop(workshopCode) {
    document.getElementById('workshop-code').value = workshopCode;
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin123';

    hideModal();
    showToast('تم ملء بيانات تسجيل الدخول', 'success');

    // تسجيل دخول تلقائي بعد ثانيتين
    setTimeout(() => {
        document.getElementById('login-form').dispatchEvent(new Event('submit'));
    }, 1000);
}

// نسخ جميع بيانات الاعتماد
function copyAllCredentials(workshopCode) {
    const workshop = systemData.workshops.find(w => w.code === workshopCode);
    if (!workshop) return;

    const credentials = `معلومات تسجيل الدخول - ${workshop.name}
كود الورشة: ${workshop.code}
اسم المستخدم: admin
كلمة المرور: admin123
نوع الترخيص: ${getLicenseTypeText(workshop.licenseType)}
تاريخ الانتهاء: ${formatDate(workshop.expiryDate)}

احفظ هذه المعلومات في مكان آمن`;

    copyToClipboard(credentials);
    showToast('تم نسخ جميع معلومات تسجيل الدخول', 'success');
}

// عرض مساعدة للكود غير الصالح
function showInvalidCodeHelp() {
    const helpHTML = `
        <div class="invalid-code-help">
            <h3><i class="fas fa-exclamation-triangle"></i> كود التفعيل غير صالح</h3>

            <div class="help-content">
                <h4>الأسباب المحتملة:</h4>
                <ul>
                    <li>الكود مكتوب بشكل خاطئ</li>
                    <li>الكود منتهي الصلاحية</li>
                    <li>الكود مستخدم من قبل</li>
                    <li>الكود غير صحيح</li>
                </ul>

                <h4>الحلول:</h4>
                <ul>
                    <li>تأكد من كتابة الكود بشكل صحيح</li>
                    <li>تواصل مع المطور للحصول على كود جديد</li>
                    <li>أرسل طلب تفعيل جديد</li>
                </ul>

                <h4>تنسيق الكود الصحيح:</h4>
                <div class="code-format">
                    <code>ACT-TRIAL-123456</code>
                    <code>ACT-PROFESSIONAL-789012</code>
                    <code>ACT-ENTERPRISE-345678</code>
                </div>
            </div>

            <div class="help-actions">
                <button class="btn btn-primary" onclick="showSupport()">
                    <i class="fas fa-headset"></i>
                    تواصل مع الدعم
                </button>
                <button class="btn btn-info" onclick="requestActivation()">
                    <i class="fas fa-paper-plane"></i>
                    أرسل طلب جديد
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('مساعدة كود التفعيل', helpHTML);
}

// التحقق من حالة الطلب
function checkRequestStatus(requestId) {
    const savedRequests = JSON.parse(localStorage.getItem('activationRequests')) || [];
    const request = savedRequests.find(r => r.id === requestId);

    if (!request) {
        showToast('لم يتم العثور على الطلب', 'error');
        return;
    }

    // محاكاة تحديث حالة الطلب
    const statuses = ['pending', 'approved', 'rejected'];
    const randomStatus = Math.random() < 0.7 ? 'approved' : 'pending'; // 70% موافقة

    const statusHTML = `
        <div class="status-check">
            <h3><i class="fas fa-search"></i> حالة طلب التفعيل</h3>

            <div class="request-info">
                <div class="info-item">
                    <span class="label">رقم الطلب:</span>
                    <span class="value">${request.requestNumber}</span>
                </div>
                <div class="info-item">
                    <span class="label">اسم الورشة:</span>
                    <span class="value">${request.workshopName}</span>
                </div>
                <div class="info-item">
                    <span class="label">تاريخ الطلب:</span>
                    <span class="value">${formatDate(request.timestamp)}</span>
                </div>
            </div>

            <div class="status-display">
                <div class="status-badge ${randomStatus}">
                    ${randomStatus === 'pending' ? '⏳ قيد المراجعة' :
                      randomStatus === 'approved' ? '✅ تم الموافقة' : '❌ تم الرفض'}
                </div>

                ${randomStatus === 'approved' ? `
                    <div class="activation-code-display">
                        <h4>كود التفعيل الخاص بك:</h4>
                        <div class="code-box">
                            <code class="activation-code">ACT-${request.licenseType.toUpperCase()}-${Date.now().toString(36).toUpperCase()}</code>
                            <button class="btn btn-sm btn-secondary" onclick="copyActivationCode(this.previousElementSibling.textContent)">
                                <i class="fas fa-copy"></i>
                                نسخ
                            </button>
                        </div>
                        <p class="code-note">استخدم هذا الكود لتفعيل النظام</p>
                    </div>
                ` : randomStatus === 'pending' ? `
                    <div class="pending-message">
                        <p>طلبك قيد المراجعة. سيتم التواصل معك قريباً.</p>
                    </div>
                ` : `
                    <div class="rejected-message">
                        <p>تم رفض الطلب. يرجى التواصل مع الدعم الفني.</p>
                    </div>
                `}
            </div>

            <div class="status-actions">
                ${randomStatus === 'approved' ? `
                    <button class="btn btn-success" onclick="useActivationCode()">
                        <i class="fas fa-key"></i>
                        استخدم كود التفعيل
                    </button>
                ` : ''}
                <button class="btn btn-info" onclick="showSupport()">
                    <i class="fas fa-headset"></i>
                    تواصل مع الدعم
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('حالة الطلب', statusHTML);
}

// نسخ كود التفعيل
function copyActivationCode(code) {
    copyToClipboard(code);
    showToast('تم نسخ كود التفعيل', 'success');
}

// استخدام كود التفعيل
function useActivationCode() {
    const codeElement = document.querySelector('.activation-code');
    if (codeElement) {
        const code = codeElement.textContent;
        hideModal();

        // فتح نموذج التفعيل مع الكود
        requestActivation();

        setTimeout(() => {
            document.getElementById('activation-code').value = code;
            showToast('تم ملء كود التفعيل. أكمل البيانات واضغط "تجربة كود التفعيل"', 'info');
        }, 500);
    }
}

// عرض معلومات انتهاء الترخيص
function showLicenseExpiredInfo(workshop) {
    const expiredHTML = `
        <div class="license-expired">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3>انتهت صلاحية الترخيص</h3>

            <div class="expired-info">
                <div class="info-item">
                    <span class="label">الورشة:</span>
                    <span class="value">${workshop.name}</span>
                </div>
                <div class="info-item">
                    <span class="label">تاريخ الانتهاء:</span>
                    <span class="value">${formatDate(workshop.expiryDate)}</span>
                </div>
                <div class="info-item">
                    <span class="label">نوع الترخيص:</span>
                    <span class="value">${getLicenseTypeText(workshop.licenseType)}</span>
                </div>
            </div>

            <div class="renewal-options">
                <h4>خيارات التجديد:</h4>
                <ul>
                    <li>تواصل مع المطور لتجديد الترخيص</li>
                    <li>اطلب ترقية لنوع ترخيص أفضل</li>
                    <li>احصل على ترخيص تجريبي جديد</li>
                </ul>
            </div>

            <div class="expired-actions">
                <button class="btn btn-primary" onclick="showSupport()">
                    <i class="fas fa-headset"></i>
                    تواصل للتجديد
                </button>
                <button class="btn btn-info" onclick="requestActivation()">
                    <i class="fas fa-plus"></i>
                    طلب ترخيص جديد
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('انتهاء صلاحية الترخيص', expiredHTML);
}

// عرض معلومات الاتصال
function showContactInfo() {
    const contactHTML = `
        <div class="contact-info">
            <h3><i class="fas fa-headset"></i> الدعم الفني</h3>
            <p>نحن هنا لمساعدتك في أي وقت</p>

            <div class="contact-methods">
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="method-info">
                        <h4>الهاتف</h4>
                        <p>+213 555 123 456</p>
                        <small>متاح 24/7</small>
                    </div>
                    <button class="btn btn-sm btn-secondary" onclick="copyToClipboard('+213 555 123 456')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-info">
                        <h4>البريد الإلكتروني</h4>
                        <p><EMAIL></p>
                        <small>رد خلال 24 ساعة</small>
                    </div>
                    <button class="btn btn-sm btn-secondary" onclick="copyToClipboard('<EMAIL>')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="method-info">
                        <h4>واتساب</h4>
                        <p>+213 555 123 456</p>
                        <small>رد سريع</small>
                    </div>
                    <button class="btn btn-sm btn-secondary" onclick="openWhatsApp()">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                </div>
            </div>

            <div class="working-hours">
                <h4><i class="fas fa-clock"></i> ساعات العمل</h4>
                <div class="hours-info">
                    <div>السبت - الخميس: 8:00 - 17:00</div>
                    <div>الجمعة: مغلق</div>
                    <div>الطوارئ: متاح 24/7</div>
                </div>
            </div>

            <div class="contact-actions">
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('الدعم الفني', contactHTML);
}

// فتح واتساب
function openWhatsApp() {
    const phoneNumber = '213555123456';
    const message = 'مرحباً، أحتاج مساعدة في نظام إدارة محطة الغاز';
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

// عرض الدعم الفني
function showSupport() {
    showContactInfo();
}

// عرض حالة التحميل
function showLoadingState(show) {
    const loginBtn = document.querySelector('.login-btn');
    if (!loginBtn) return;

    if (show) {
        loginBtn.disabled = true;
        loginBtn.innerHTML = `
            <div class="loading-spinner"></div>
            جاري تسجيل الدخول...
        `;
    } else {
        loginBtn.disabled = false;
        loginBtn.innerHTML = `
            <i class="fas fa-sign-in-alt"></i>
            تسجيل الدخول
        `;
    }
}

// تحديث إحصائيات النظام
function updateSystemStats() {
    const activeWorkshops = systemData.workshops.filter(w => w.isActive).length;
    const expiredLicenses = systemData.workshops.filter(w => new Date(w.expiryDate) < new Date()).length;

    console.log(`📊 إحصائيات النظام:
    - الورشات النشطة: ${activeWorkshops}
    - التراخيص المنتهية: ${expiredLicenses}
    - إجمالي الورشات: ${systemData.workshops.length}`);
}

// فحص التراخيص المنتهية
function checkExpiredLicenses() {
    const expiredWorkshops = systemData.workshops.filter(w =>
        w.isActive && new Date(w.expiryDate) < new Date()
    );

    if (expiredWorkshops.length > 0) {
        console.warn(`⚠️ تم العثور على ${expiredWorkshops.length} ترخيص منتهي الصلاحية`);
    }
}

// حساب تاريخ انتهاء الترخيص
function calculateExpiryDate(licenseType) {
    const expiryDate = new Date();

    switch (licenseType) {
        case 'trial':
            expiryDate.setDate(expiryDate.getDate() + 30);
            break;
        case 'professional':
            expiryDate.setFullYear(expiryDate.getFullYear() + 1);
            break;
        case 'enterprise':
            expiryDate.setFullYear(expiryDate.getFullYear() + 10);
            break;
        default:
            expiryDate.setDate(expiryDate.getDate() + 30);
    }

    return expiryDate.toISOString();
}

// الحصول على الصلاحيات حسب نوع الترخيص
function getPermissionsByLicenseType(licenseType) {
    const permissions = {
        trial: ['customers', 'cards', 'appointments'],
        professional: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports'],
        enterprise: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports', 'employees', 'payroll', 'maintenance']
    };

    return permissions[licenseType] || permissions.trial;
}

// حفظ بيانات النظام
function saveSystemData() {
    try {
        localStorage.setItem('clientWorkshops', JSON.stringify(systemData.workshops));
        console.log('✅ تم حفظ بيانات النظام');
    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        showToast('خطأ في حفظ البيانات', 'error');
    }
}

// دوال مساعدة للتنسيق
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-DZ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('ar-DZ', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getLicenseTypeText(type) {
    const types = {
        'trial': 'تجريبي',
        'professional': 'احترافي',
        'enterprise': 'مؤسسي'
    };
    return types[type] || type;
}

function getRoleText(role) {
    const roles = {
        'admin': 'مدير',
        'user': 'مستخدم',
        'viewer': 'مشاهد'
    };
    return roles[role] || role;
}

// دوال توليد البيانات
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateWorkshopCode() {
    const prefix = 'WS';
    const year = new Date().getFullYear().toString().slice(-2);
    const sequence = Math.floor(Math.random() * 999).toString().padStart(3, '0');
    return `${prefix}-${year}-${sequence}`;
}

function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 20; i++) {
        if (i > 0 && i % 4 === 0) result += '-';
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// دوال النوافذ المنبثقة
function showModal(title, content) {
    // إزالة النوافذ الموجودة
    hideModal();

    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إظهار النافذة مع تأثير
    setTimeout(() => {
        modal.style.display = 'flex';
        modal.classList.add('show');
    }, 10);
}

function hideModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    });
}

// دوال رسائل التنبيه
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    const icon = type === 'success' ? 'fas fa-check-circle' :
                 type === 'error' ? 'fas fa-exclamation-circle' :
                 type === 'warning' ? 'fas fa-exclamation-triangle' :
                 'fas fa-info-circle';

    toast.innerHTML = `
        <div class="toast-icon">
            <i class="${icon}"></i>
        </div>
        <div class="toast-content">
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    toastContainer.appendChild(toast);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// ==================== دوال محسنة جديدة ====================

// عرض مؤشر التحميل العام
function showGlobalLoading(message = 'جاري التحميل...') {
    const loadingOverlay = document.getElementById('loading-overlay');
    const loadingMessage = document.getElementById('loading-message');

    if (loadingOverlay && loadingMessage) {
        loadingMessage.textContent = message;
        loadingOverlay.style.display = 'flex';
    }
}

// إخفاء مؤشر التحميل العام
function hideGlobalLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// تحديث معلومات البناء
function updateBuildInfo() {
    const buildElement = document.getElementById('build-number');
    if (buildElement) {
        buildElement.textContent = systemConfig.buildNumber;
    }
}

// عرض حالة التحميل في زر تسجيل الدخول
function showLoadingState(isLoading) {
    const loginBtn = document.querySelector('.login-btn');
    const originalText = loginBtn.innerHTML;

    if (isLoading) {
        loginBtn.disabled = true;
        loginBtn.innerHTML = `
            <div class="loading-spinner-small"></div>
            جاري تسجيل الدخول...
        `;
    } else {
        loginBtn.disabled = false;
        loginBtn.innerHTML = originalText;
    }
}

// تحديث إحصائيات النظام
function updateSystemStats() {
    systemData.systemStats.totalLogins = (systemData.systemStats.totalLogins || 0) + 1;
    systemData.systemStats.lastLoginTime = new Date().toISOString();
    saveSystemData();
}

// فحص التراخيص المنتهية
function checkExpiredLicenses() {
    const expiredWorkshops = systemData.workshops.filter(w =>
        new Date(w.expiryDate) < new Date()
    );

    if (expiredWorkshops.length > 0) {
        console.warn(`⚠️ تم العثور على ${expiredWorkshops.length} ورشة منتهية الصلاحية`);

        // عرض تنبيه للمستخدم
        setTimeout(() => {
            showToast(`تنبيه: يوجد ${expiredWorkshops.length} ترخيص منتهي الصلاحية`, 'warning');
        }, 2000);
    }
}

// عرض معلومات انتهاء الترخيص
function showLicenseExpiredInfo(workshop) {
    const expiredHTML = `
        <div class="license-expired">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3>انتهت صلاحية الترخيص</h3>
            <div class="expired-details">
                <div class="detail-item">
                    <span class="label">الورشة:</span>
                    <span class="value">${workshop.name}</span>
                </div>
                <div class="detail-item">
                    <span class="label">تاريخ الانتهاء:</span>
                    <span class="value">${formatDate(workshop.expiryDate)}</span>
                </div>
                <div class="detail-item">
                    <span class="label">نوع الترخيص:</span>
                    <span class="value">${getLicenseTypeText(workshop.licenseType)}</span>
                </div>
            </div>
            <div class="renewal-info">
                <h4>لتجديد الترخيص:</h4>
                <ol>
                    <li>تواصل مع الدعم الفني</li>
                    <li>احصل على كود تجديد جديد</li>
                    <li>أدخل كود التجديد في النظام</li>
                </ol>
            </div>
            <div class="contact-info">
                <h4>معلومات التواصل:</h4>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${systemConfig.supportContact.phone}</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${systemConfig.supportContact.email}</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <span>${systemConfig.supportContact.workingHours}</span>
                </div>
            </div>
            <div class="expired-actions">
                <button class="btn btn-primary" onclick="requestActivation()">
                    <i class="fas fa-key"></i>
                    طلب تجديد الترخيص
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('انتهت صلاحية الترخيص', expiredHTML);
}

// عرض معلومات التواصل
function showContactInfo() {
    const contactHTML = `
        <div class="contact-support">
            <h3><i class="fas fa-headset"></i> الدعم الفني</h3>
            <p>للحصول على المساعدة، يرجى التواصل معنا:</p>
            <div class="contact-methods">
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="method-info">
                        <h4>الهاتف</h4>
                        <p>${systemConfig.supportContact.phone}</p>
                        <small>متاح 24/7</small>
                    </div>
                </div>
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-info">
                        <h4>البريد الإلكتروني</h4>
                        <p>${systemConfig.supportContact.email}</p>
                        <small>رد خلال 24 ساعة</small>
                    </div>
                </div>
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="method-info">
                        <h4>ساعات العمل</h4>
                        <p>${systemConfig.supportContact.workingHours}</p>
                        <small>التوقيت المحلي</small>
                    </div>
                </div>
            </div>
            <div class="support-actions">
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('الدعم الفني', contactHTML);
}

// دالة عرض الدعم المحسنة
function showSupport() {
    showContactInfo();
}

// حفظ بيانات النظام
function saveSystemData() {
    try {
        localStorage.setItem('clientWorkshops', JSON.stringify(systemData.workshops));
        localStorage.setItem('systemStats', JSON.stringify(systemData.systemStats));
        console.log('✅ تم حفظ بيانات النظام');
    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        showToast('خطأ في حفظ البيانات', 'error');
    }
}

console.log('✅ تم تحميل نظام تسجيل الدخول - نسخة العميل المحسنة v' + systemConfig.version);