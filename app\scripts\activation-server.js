// ==================== خادم التفعيل المركزي ====================

// بيانات الخادم
let serverData = {
    isRunning: true,
    port: 8080,
    ip: '*************',
    connectedClients: [],
    activationRequests: [],
    activityLog: [],
    licenses: JSON.parse(localStorage.getItem('centralLicenses')) || [],
    stats: {
        totalClients: 0,
        activeLicenses: 0,
        pendingRequests: 0
    }
};

// إعدادات الخادم
const serverConfig = {
    maxClients: 100,
    logRetention: 1000, // عدد السجلات المحفوظة
    autoRefreshInterval: 5000, // 5 ثوان
    licenseTypes: {
        trial: { duration: 30, features: ['customers', 'cards', 'appointments'] },
        professional: { duration: 365, features: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports'] },
        enterprise: { duration: 3650, features: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'reports', 'employees', 'payroll', 'maintenance'] }
    }
};

// تهيئة الخادم
document.addEventListener('DOMContentLoaded', function() {
    initializeServer();
    setupEventListeners();
    startAutoRefresh();
    loadSampleData();
});

// تهيئة الخادم
function initializeServer() {
    console.log('🖥️ تهيئة خادم التفعيل المركزي...');
    
    // تحديث عنوان IP
    updateServerIP();
    
    // تحديث الإحصائيات
    updateStats();
    
    // تحميل البيانات
    loadActivationRequests();
    loadConnectedClients();
    loadActivityLog();
    
    // إضافة سجل بدء التشغيل
    addLogEntry('info', 'تم بدء تشغيل خادم التفعيل المركزي');
    
    console.log('✅ تم تهيئة الخادم بنجاح');
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تحديث نوع الترخيص
    const licenseTypeSelect = document.getElementById('license-type');
    if (licenseTypeSelect) {
        licenseTypeSelect.addEventListener('change', updateLicenseDuration);
    }
    
    // تحديث نوع الترخيص السريع
    const quickLicenseTypeSelect = document.getElementById('quick-license-type');
    if (quickLicenseTypeSelect) {
        quickLicenseTypeSelect.addEventListener('change', updateQuickLicenseDuration);
    }
}

// تحديث عنوان IP الخادم
function updateServerIP() {
    // محاكاة الحصول على IP المحلي
    const serverIpElement = document.getElementById('server-ip');
    if (serverIpElement) {
        // يمكن استبدال هذا بـ API حقيقي للحصول على IP
        serverData.ip = '192.168.1.' + Math.floor(Math.random() * 254 + 1);
        serverIpElement.textContent = serverData.ip;
    }
}

// بدء التحديث التلقائي
function startAutoRefresh() {
    setInterval(() => {
        if (serverData.isRunning) {
            updateStats();
            simulateClientActivity();
        }
    }, serverConfig.autoRefreshInterval);
}

// تحديث الإحصائيات
function updateStats() {
    serverData.stats.totalClients = serverData.connectedClients.length;
    serverData.stats.activeLicenses = serverData.licenses.filter(l => isLicenseActive(l)).length;
    serverData.stats.pendingRequests = serverData.activationRequests.filter(r => r.status === 'pending').length;
    
    // تحديث العرض
    document.getElementById('total-clients').textContent = serverData.stats.totalClients;
    document.getElementById('active-licenses').textContent = serverData.stats.activeLicenses;
    document.getElementById('pending-requests').textContent = serverData.stats.pendingRequests;
    document.getElementById('client-count').textContent = `${serverData.stats.totalClients} عميل متصل`;
}

// توليد ترخيص سريع
function generateQuickLicense() {
    const licenseType = document.getElementById('quick-license-type').value;
    const workshopName = document.getElementById('quick-workshop-name').value.trim();
    
    if (!workshopName) {
        showToast('يرجى إدخال اسم الورشة', 'error');
        return;
    }
    
    const license = createLicense(licenseType, workshopName, 'مالك الورشة');
    
    // عرض كود التفعيل
    const activationCode = generateActivationCode(license);
    document.getElementById('quick-activation-code').value = activationCode;
    document.getElementById('quick-generated-code').style.display = 'block';
    
    // إضافة سجل
    addLogEntry('success', `تم إنشاء ترخيص ${licenseType} للورشة: ${workshopName}`);
    
    showToast('تم إنشاء الترخيص بنجاح', 'success');
    
    // مسح النموذج
    document.getElementById('quick-workshop-name').value = '';
}

// نسخ الكود السريع
function copyQuickCode() {
    const codeInput = document.getElementById('quick-activation-code');
    codeInput.select();
    document.execCommand('copy');
    showToast('تم نسخ كود التفعيل', 'success');
}

// إنشاء ترخيص
function createLicense(type, workshopName, ownerName) {
    const config = serverConfig.licenseTypes[type];
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + config.duration);
    
    const license = {
        id: generateId(),
        workshopCode: generateWorkshopCode(),
        workshopName: workshopName,
        ownerName: ownerName,
        licenseType: type,
        licenseKey: generateLicenseKey(),
        features: [...config.features],
        issueDate: new Date().toISOString(),
        expiryDate: expiryDate.toISOString(),
        isActive: true,
        activationCode: null,
        createdAt: new Date().toISOString()
    };
    
    serverData.licenses.push(license);
    saveLicenses();
    
    return license;
}

// توليد كود التفعيل
function generateActivationCode(license) {
    const code = `ACT-${license.licenseType.toUpperCase()}-${Date.now().toString(36).toUpperCase()}`;
    license.activationCode = code;
    saveLicenses();
    return code;
}

// تحميل طلبات التفعيل
function loadActivationRequests() {
    const tbody = document.getElementById('requests-tbody');
    if (!tbody) return;
    
    tbody.innerHTML = serverData.activationRequests.map(request => {
        const statusClass = request.status === 'pending' ? 'pending' : 
                           request.status === 'approved' ? 'approved' : 'rejected';
        
        return `
            <tr>
                <td>${formatTime(request.timestamp)}</td>
                <td>${request.workshopName}</td>
                <td>${request.ownerName}</td>
                <td><span class="ip-address">${request.clientIP}</span></td>
                <td>${request.requestType}</td>
                <td><span class="status-badge ${statusClass}">${getStatusText(request.status)}</span></td>
                <td>
                    ${request.status === 'pending' ? `
                        <button class="btn btn-sm btn-success" onclick="showActivationModal('${request.id}')">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="rejectRequest('${request.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-info" onclick="viewRequestDetails('${request.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    `}
                </td>
            </tr>
        `;
    }).join('');
}

// تحميل العملاء المتصلين
function loadConnectedClients() {
    const clientsGrid = document.getElementById('clients-grid');
    if (!clientsGrid) return;
    
    clientsGrid.innerHTML = serverData.connectedClients.map(client => `
        <div class="client-card" onclick="showClientDetails('${client.id}')">
            <div class="client-header">
                <span class="client-name">${client.workshopName}</span>
                <span class="client-status connected">متصل</span>
            </div>
            <div class="client-info">
                <div><strong>IP:</strong> <span class="ip-address">${client.ip}</span></div>
                <div><strong>الكود:</strong> <span class="workshop-code">${client.workshopCode}</span></div>
                <div><strong>النوع:</strong> ${getLicenseTypeText(client.licenseType)}</div>
                <div><strong>آخر نشاط:</strong> ${formatTime(client.lastActivity)}</div>
            </div>
        </div>
    `).join('');
}

// تحميل سجل الأنشطة
function loadActivityLog() {
    const logContainer = document.getElementById('log-container');
    if (!logContainer) return;
    
    const recentLogs = serverData.activityLog.slice(-50).reverse();
    
    logContainer.innerHTML = recentLogs.map(log => `
        <div class="log-entry">
            <span class="log-time">${formatTime(log.timestamp)}</span>
            <span class="log-message">${log.message}</span>
            <span class="log-type ${log.type}">${log.type}</span>
        </div>
    `).join('');
}

// إضافة سجل نشاط
function addLogEntry(type, message) {
    const logEntry = {
        id: generateId(),
        type: type,
        message: message,
        timestamp: new Date().toISOString()
    };
    
    serverData.activityLog.push(logEntry);
    
    // الاحتفاظ بعدد محدود من السجلات
    if (serverData.activityLog.length > serverConfig.logRetention) {
        serverData.activityLog = serverData.activityLog.slice(-serverConfig.logRetention);
    }
    
    loadActivityLog();
}

// محاكاة نشاط العملاء
function simulateClientActivity() {
    // إضافة عملاء جدد أحياناً
    if (Math.random() < 0.1 && serverData.connectedClients.length < serverConfig.maxClients) {
        addRandomClient();
    }
    
    // إزالة عملاء أحياناً
    if (Math.random() < 0.05 && serverData.connectedClients.length > 0) {
        removeRandomClient();
    }
    
    // تحديث آخر نشاط للعملاء
    serverData.connectedClients.forEach(client => {
        if (Math.random() < 0.3) {
            client.lastActivity = new Date().toISOString();
        }
    });
    
    loadConnectedClients();
}

// إضافة عميل عشوائي
function addRandomClient() {
    const workshopNames = ['ورشة النور', 'ورشة الأمل', 'ورشة المستقبل', 'ورشة التقدم', 'ورشة الإبداع'];
    const licenseTypes = ['trial', 'professional', 'enterprise'];
    
    const client = {
        id: generateId(),
        workshopName: workshopNames[Math.floor(Math.random() * workshopNames.length)],
        workshopCode: generateWorkshopCode(),
        ip: `192.168.1.${Math.floor(Math.random() * 254 + 1)}`,
        licenseType: licenseTypes[Math.floor(Math.random() * licenseTypes.length)],
        connectedAt: new Date().toISOString(),
        lastActivity: new Date().toISOString()
    };
    
    serverData.connectedClients.push(client);
    addLogEntry('info', `عميل جديد متصل: ${client.workshopName} (${client.ip})`);
}

// إزالة عميل عشوائي
function removeRandomClient() {
    const randomIndex = Math.floor(Math.random() * serverData.connectedClients.length);
    const client = serverData.connectedClients[randomIndex];
    
    serverData.connectedClients.splice(randomIndex, 1);
    addLogEntry('warning', `انقطع الاتصال مع: ${client.workshopName} (${client.ip})`);
}

// دوال مساعدة
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function generateWorkshopCode() {
    const prefix = 'WS';
    const year = new Date().getFullYear().toString().slice(-2);
    const sequence = Math.floor(Math.random() * 999).toString().padStart(3, '0');
    return `${prefix}-${year}-${sequence}`;
}

function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 20; i++) {
        if (i > 0 && i % 4 === 0) result += '-';
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

function isLicenseActive(license) {
    return license.isActive && new Date(license.expiryDate) > new Date();
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('ar-DZ', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

function getStatusText(status) {
    const statusTexts = {
        'pending': 'معلق',
        'approved': 'موافق عليه',
        'rejected': 'مرفوض'
    };
    return statusTexts[status] || status;
}

function getLicenseTypeText(type) {
    const types = {
        'trial': 'تجريبي',
        'professional': 'احترافي',
        'enterprise': 'مؤسسي'
    };
    return types[type] || type;
}

function saveLicenses() {
    localStorage.setItem('centralLicenses', JSON.stringify(serverData.licenses));
}

// تحديث الطلبات
function refreshRequests() {
    loadActivationRequests();
    updateStats();
    addLogEntry('info', 'تم تحديث طلبات التفعيل');
    showToast('تم تحديث الطلبات', 'success');
}

// مسح السجل
function clearLog() {
    if (confirm('هل أنت متأكد من مسح سجل الأنشطة؟')) {
        serverData.activityLog = [];
        loadActivityLog();
        addLogEntry('warning', 'تم مسح سجل الأنشطة');
        showToast('تم مسح السجل', 'success');
    }
}

// إيقاف الخادم
function stopServer() {
    if (confirm('هل أنت متأكد من إيقاف الخادم؟')) {
        serverData.isRunning = false;
        addLogEntry('error', 'تم إيقاف خادم التفعيل المركزي');
        showToast('تم إيقاف الخادم', 'warning');
        
        // تعطيل الواجهة
        document.body.style.opacity = '0.5';
        document.body.style.pointerEvents = 'none';
        
        setTimeout(() => {
            window.close();
        }, 2000);
    }
}

// إظهار رسالة تنبيه
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = type === 'success' ? 'fas fa-check-circle' : 
                 type === 'error' ? 'fas fa-exclamation-circle' : 
                 type === 'warning' ? 'fas fa-exclamation-triangle' : 
                 'fas fa-info-circle';
    
    toast.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
    `;
    
    toastContainer.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// ==================== دوال إضافية لخادم التفعيل ====================

// تحميل بيانات تجريبية
function loadSampleData() {
    // إضافة طلبات تفعيل تجريبية
    if (serverData.activationRequests.length === 0) {
        const sampleRequests = [
            {
                id: generateId(),
                workshopName: 'ورشة الأمل للغاز',
                ownerName: 'أحمد محمد',
                clientIP: '************',
                requestType: 'ترخيص جديد',
                status: 'pending',
                timestamp: new Date().toISOString()
            },
            {
                id: generateId(),
                workshopName: 'ورشة النور التقنية',
                ownerName: 'فاطمة علي',
                clientIP: '************',
                requestType: 'تجديد ترخيص',
                status: 'approved',
                timestamp: new Date(Date.now() - 3600000).toISOString()
            }
        ];

        serverData.activationRequests = sampleRequests;
    }

    // إضافة عملاء متصلين تجريبيين
    if (serverData.connectedClients.length === 0) {
        addRandomClient();
        addRandomClient();
    }

    loadActivationRequests();
    loadConnectedClients();
}

// تحديث مدة الترخيص
function updateLicenseDuration() {
    const licenseType = document.getElementById('license-type').value;
    const durationInput = document.getElementById('license-duration');

    if (durationInput && serverConfig.licenseTypes[licenseType]) {
        durationInput.value = serverConfig.licenseTypes[licenseType].duration;
    }
}

// تحديث مدة الترخيص السريع
function updateQuickLicenseDuration() {
    const licenseType = document.getElementById('quick-license-type').value;
    // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
}

// إظهار نافذة التفعيل
function showActivationModal(requestId) {
    const request = serverData.activationRequests.find(r => r.id === requestId);
    if (!request) return;

    // ملء معلومات العميل
    const clientInfo = document.getElementById('client-info');
    clientInfo.innerHTML = `
        <h4>معلومات طلب التفعيل</h4>
        <div class="info-grid">
            <div><strong>اسم الورشة:</strong> ${request.workshopName}</div>
            <div><strong>اسم المالك:</strong> ${request.ownerName}</div>
            <div><strong>عنوان IP:</strong> <span class="ip-address">${request.clientIP}</span></div>
            <div><strong>نوع الطلب:</strong> ${request.requestType}</div>
            <div><strong>وقت الطلب:</strong> ${formatTime(request.timestamp)}</div>
        </div>
    `;

    // تحديث الميزات حسب نوع الترخيص
    updateLicenseDuration();
    updateFeaturesList();

    // حفظ معرف الطلب
    document.getElementById('activation-modal').dataset.requestId = requestId;

    // إظهار النافذة
    document.getElementById('activation-modal').style.display = 'flex';
}

// إخفاء نافذة التفعيل
function hideActivationModal() {
    document.getElementById('activation-modal').style.display = 'none';
}

// تحديث قائمة الميزات
function updateFeaturesList() {
    const licenseType = document.getElementById('license-type').value;
    const features = serverConfig.licenseTypes[licenseType]?.features || [];

    const checkboxes = document.querySelectorAll('.features-list input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = features.includes(checkbox.value);
    });
}

// الموافقة على التفعيل
function approveActivation() {
    const requestId = document.getElementById('activation-modal').dataset.requestId;
    const request = serverData.activationRequests.find(r => r.id === requestId);

    if (!request) return;

    const licenseType = document.getElementById('license-type').value;
    const duration = parseInt(document.getElementById('license-duration').value);

    // الحصول على الميزات المحددة
    const selectedFeatures = [];
    document.querySelectorAll('.features-list input[type="checkbox"]:checked').forEach(checkbox => {
        selectedFeatures.push(checkbox.value);
    });

    // إنشاء الترخيص
    const license = createCustomLicense(
        licenseType,
        request.workshopName,
        request.ownerName,
        duration,
        selectedFeatures
    );

    // توليد كود التفعيل
    const activationCode = generateActivationCode(license);

    // تحديث حالة الطلب
    request.status = 'approved';
    request.activationCode = activationCode;
    request.licenseId = license.id;

    // إضافة سجل
    addLogEntry('success', `تم الموافقة على طلب التفعيل للورشة: ${request.workshopName}`);

    // تحديث العرض
    loadActivationRequests();
    updateStats();
    hideActivationModal();

    showToast(`تم إنشاء كود التفعيل: ${activationCode}`, 'success');

    // نسخ الكود تلقائياً
    navigator.clipboard.writeText(activationCode).then(() => {
        showToast('تم نسخ كود التفعيل تلقائياً', 'info');
    });
}

// رفض التفعيل
function rejectActivation() {
    const requestId = document.getElementById('activation-modal').dataset.requestId;
    const request = serverData.activationRequests.find(r => r.id === requestId);

    if (!request) return;

    if (confirm(`هل أنت متأكد من رفض طلب التفعيل للورشة "${request.workshopName}"؟`)) {
        request.status = 'rejected';
        request.rejectionReason = 'تم الرفض من قبل المطور';

        addLogEntry('warning', `تم رفض طلب التفعيل للورشة: ${request.workshopName}`);

        loadActivationRequests();
        updateStats();
        hideActivationModal();

        showToast('تم رفض طلب التفعيل', 'warning');
    }
}

// رفض طلب مباشرة
function rejectRequest(requestId) {
    const request = serverData.activationRequests.find(r => r.id === requestId);
    if (!request) return;

    if (confirm(`هل أنت متأكد من رفض طلب "${request.workshopName}"؟`)) {
        request.status = 'rejected';
        request.rejectionReason = 'رفض سريع';

        addLogEntry('warning', `تم رفض طلب التفعيل للورشة: ${request.workshopName}`);

        loadActivationRequests();
        updateStats();

        showToast('تم رفض الطلب', 'warning');
    }
}

// إنشاء ترخيص مخصص
function createCustomLicense(type, workshopName, ownerName, duration, features) {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + duration);

    const license = {
        id: generateId(),
        workshopCode: generateWorkshopCode(),
        workshopName: workshopName,
        ownerName: ownerName,
        licenseType: type,
        licenseKey: generateLicenseKey(),
        features: features,
        duration: duration,
        issueDate: new Date().toISOString(),
        expiryDate: expiryDate.toISOString(),
        isActive: true,
        activationCode: null,
        createdAt: new Date().toISOString()
    };

    serverData.licenses.push(license);
    saveLicenses();

    return license;
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    const request = serverData.activationRequests.find(r => r.id === requestId);
    if (!request) return;

    const license = request.licenseId ? serverData.licenses.find(l => l.id === request.licenseId) : null;

    const detailsHTML = `
        <div class="request-details">
            <h4>تفاصيل الطلب</h4>
            <div class="details-grid">
                <div><strong>اسم الورشة:</strong> ${request.workshopName}</div>
                <div><strong>اسم المالك:</strong> ${request.ownerName}</div>
                <div><strong>عنوان IP:</strong> <span class="ip-address">${request.clientIP}</span></div>
                <div><strong>نوع الطلب:</strong> ${request.requestType}</div>
                <div><strong>الحالة:</strong> <span class="status-badge ${request.status}">${getStatusText(request.status)}</span></div>
                <div><strong>وقت الطلب:</strong> ${new Date(request.timestamp).toLocaleString('ar-DZ')}</div>
                ${request.activationCode ? `<div><strong>كود التفعيل:</strong> <span class="license-key">${request.activationCode}</span></div>` : ''}
                ${request.rejectionReason ? `<div><strong>سبب الرفض:</strong> ${request.rejectionReason}</div>` : ''}
            </div>

            ${license ? `
                <h4 style="margin-top: 2rem;">تفاصيل الترخيص</h4>
                <div class="details-grid">
                    <div><strong>كود الورشة:</strong> <span class="workshop-code">${license.workshopCode}</span></div>
                    <div><strong>نوع الترخيص:</strong> ${getLicenseTypeText(license.licenseType)}</div>
                    <div><strong>مفتاح الترخيص:</strong> <span class="license-key">${license.licenseKey}</span></div>
                    <div><strong>تاريخ الإصدار:</strong> ${new Date(license.issueDate).toLocaleDateString('ar-DZ')}</div>
                    <div><strong>تاريخ الانتهاء:</strong> ${new Date(license.expiryDate).toLocaleDateString('ar-DZ')}</div>
                    <div><strong>الميزات:</strong> ${license.features.join(', ')}</div>
                </div>
            ` : ''}
        </div>

        <div class="details-actions" style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: flex-end;">
            ${request.activationCode ? `
                <button class="btn btn-info" onclick="copyActivationCode('${request.activationCode}')">
                    <i class="fas fa-copy"></i> نسخ كود التفعيل
                </button>
            ` : ''}
            <button class="btn btn-secondary" onclick="hideModal()">إغلاق</button>
        </div>
    `;

    showModal('تفاصيل الطلب', detailsHTML);
}

// عرض تفاصيل العميل
function showClientDetails(clientId) {
    const client = serverData.connectedClients.find(c => c.id === clientId);
    if (!client) return;

    const detailsHTML = `
        <div class="client-details">
            <h4>تفاصيل العميل المتصل</h4>
            <div class="details-grid">
                <div><strong>اسم الورشة:</strong> ${client.workshopName}</div>
                <div><strong>كود الورشة:</strong> <span class="workshop-code">${client.workshopCode}</span></div>
                <div><strong>عنوان IP:</strong> <span class="ip-address">${client.ip}</span></div>
                <div><strong>نوع الترخيص:</strong> ${getLicenseTypeText(client.licenseType)}</div>
                <div><strong>وقت الاتصال:</strong> ${new Date(client.connectedAt).toLocaleString('ar-DZ')}</div>
                <div><strong>آخر نشاط:</strong> ${new Date(client.lastActivity).toLocaleString('ar-DZ')}</div>
            </div>
        </div>

        <div class="client-actions" style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: flex-end;">
            <button class="btn btn-warning" onclick="disconnectClient('${client.id}')">
                <i class="fas fa-unlink"></i> قطع الاتصال
            </button>
            <button class="btn btn-secondary" onclick="hideClientDetailsModal()">إغلاق</button>
        </div>
    `;

    document.getElementById('client-details-body').innerHTML = detailsHTML;
    document.getElementById('client-details-modal').style.display = 'flex';
}

// إخفاء نافذة تفاصيل العميل
function hideClientDetailsModal() {
    document.getElementById('client-details-modal').style.display = 'none';
}

// قطع اتصال العميل
function disconnectClient(clientId) {
    const clientIndex = serverData.connectedClients.findIndex(c => c.id === clientId);
    if (clientIndex === -1) return;

    const client = serverData.connectedClients[clientIndex];

    if (confirm(`هل أنت متأكد من قطع الاتصال مع "${client.workshopName}"؟`)) {
        serverData.connectedClients.splice(clientIndex, 1);

        addLogEntry('warning', `تم قطع الاتصال مع: ${client.workshopName} (${client.ip})`);

        loadConnectedClients();
        updateStats();
        hideClientDetailsModal();

        showToast('تم قطع الاتصال مع العميل', 'warning');
    }
}

// نسخ كود التفعيل
function copyActivationCode(code) {
    navigator.clipboard.writeText(code).then(() => {
        showToast('تم نسخ كود التفعيل', 'success');
    });
}

// تصدير التراخيص
function exportLicenses() {
    const exportData = {
        licenses: serverData.licenses,
        requests: serverData.activationRequests,
        exportDate: new Date().toISOString(),
        totalLicenses: serverData.licenses.length,
        activeLicenses: serverData.licenses.filter(l => isLicenseActive(l)).length
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `licenses-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    addLogEntry('info', 'تم تصدير بيانات التراخيص');
    showToast('تم تصدير البيانات بنجاح', 'success');
}

// إظهار نافذة منبثقة
function showModal(title, content) {
    // إنشاء نافذة مؤقتة
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// إخفاء النافذة
function hideModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (!modal.id) { // النوافذ المؤقتة فقط
            modal.remove();
        }
    });
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        if (e.target.id === 'activation-modal') {
            hideActivationModal();
        } else if (e.target.id === 'client-details-modal') {
            hideClientDetailsModal();
        }
    }
});

console.log('✅ تم تحميل خادم التفعيل المركزي بنجاح');
