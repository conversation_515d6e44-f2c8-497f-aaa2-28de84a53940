﻿<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>مركز وقود المستقبل</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: #f1f1f1;
      margin: 0;
      padding: 0;
      direction: rtl;
    }

    header {
      background-color: #2c3e50;
      color: white;
      padding: 15px;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: center;
      background-color: #34495e;
    }

    nav button {
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      font-size: 18px;
      cursor: pointer;
      transition: background 0.3s;
    }

    nav button:hover, nav button.active {
      background-color: #1abc9c;
    }

    .section {
      display: none;
      padding: 30px;
    }

    .section.active {
      display: block;
    }

    input, select, button {
      padding: 10px;
      margin: 10px 0;
      width: 100%;
      box-sizing: border-box;
      border-radius: 5px;
      border: 1px solid #ccc;
    }

    button[type="submit"], .print-btn {
      background-color: #27ae60;
      color: white;
      border: none;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background-color: white;
    }

    table th, table td {
      border: 2px solid #ccc;
      padding: 10px;
      text-align: center;
      font-size: 16px;
    }

    table th {
      background-color: #2980b9;
      color: white;
    }

    table td {
      background-color: #ecf0f1;
    }

    .print-btn {
      background-color: #2980b9;
      margin-top: 20px;
    }

    .header-info {
      margin: 20px 0;
      text-align: right;
    }

    .header-info b {
      display: block;
      font-size: 18px;
      margin-bottom: 5px;
    }

    .button-group {
      display: flex;
      justify-content: space-around;
    }

    .button-group button {
      background-color: #f39c12;
      color: white;
      border: none;
      padding: 5px 10px;
      cursor: pointer;
      margin-top: 10px;
    }

    .button-group button:hover {
      background-color: #e67e22;
    }

    /* إخفاء زر الطباعة عند الطباعة */
    @media print {
      .print-btn {
        display: none;
      }
    }

  </style>
</head>
<body>

<header>
  <h1>مركز وقود المستقبل - عزيري عبد الله اسحاق</h1>
  <p>لتحويل السيارات لاستخدام غاز البترول المميع</p>
</header>

<nav>
  <button onclick="showSection('inputSection')" class="active"><i class="fa fa-pen"></i> إدخال البيانات</button>
  <button onclick="showSection('tableSection')"><i class="fa fa-table"></i> عرض جدول الإرسال</button>
</nav>

<div class="section active" id="inputSection">
  <form id="dataForm">
    <h2>إدخال بيانات السيارة</h2>
    <select id="installation" required>
      <option value="تركيب">تركيب</option>
      <option value="مراقبة">مراقبة</option>
    </select>

    <input type="text" id="tankNumber" placeholder="رقم خزان الغاز" required>
    <input type="text" id="carType" placeholder="الصنف" required>
    <input type="text" id="serialNumber" placeholder="الرقم التسلسلي في الطراز" required>
    <input type="text" id="carRegistration" placeholder="رقم تسجيل السيارة" required>
    <input type="text" id="ownerName" placeholder="الإسم و اللقب" required>

    <button type="submit">إضافة البيانات</button>
  </form>
</div>

<div class="section" id="tableSection">
  <div class="header-info">
    <b>الجمهورية الجزائرية الديمقراطية الشعبية</b>
    <b>مركز وقود المستقبل - عزيري عبد الله اسحاق</b>
    <p>رقم: 463/2019</p>
    <p>إلى السيد: مدير الصناعة و المناجم لولاية المدية</p>
    <p><b>جدول إرسال</b></p>
    <p>تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات المجهزة بغاز البترول المميع شهر 2025.</p>
  </div>

  <table id="dataTable">
    <thead>
      <tr>
        <th>تركيب أو مراقبة</th>
        <th>رقم خزان الغاز</th>
        <th>الصنف</th>
        <th>الرقم التسلسلي</th>
        <th>رقم التسجيل</th>
        <th>الإسم و اللقب</th>
        <th>الرقم</th>
        <th>إجراءات</th>
      </tr>
    </thead>
    <tbody>
      <!-- البيانات تُضاف هنا -->
    </tbody>
  </table>

  <button class="print-btn" onclick="printTable()"><i class="fa fa-print"></i> طباعة</button>
</div>

<script>
  let rowCount = 1;

  // إظهار قسم معين
  function showSection(id) {
    document.querySelectorAll('.section').forEach(sec => sec.classList.remove('active'));
    document.getElementById(id).classList.add('active');
    document.querySelectorAll('nav button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
  }

  // تحميل البيانات المخزنة من LocalStorage
  function loadData() {
    const savedData = JSON.parse(localStorage.getItem('carData'));
    if (savedData) {
      const table = document.getElementById('dataTable').querySelector('tbody');
      savedData.forEach((data, index) => {
        const row = table.insertRow();
        row.innerHTML = `
          <td>${data.installation}</td>
          <td>${data.tankNumber}</td>
          <td>${data.carType}</td>
          <td>${data.serialNumber}</td>
          <td>${data.carRegistration}</td>
          <td>${data.ownerName}</td>
          <td>${index + 1}</td>
          <td>
            <div class="button-group">
              <button onclick="editRow(${index})"><i class="fa fa-edit"></i> تعديل</button>
              <button onclick="deleteRow(${index})"><i class="fa fa-trash"></i> حذف</button>
            </div>
          </td>
        `;
      });
      rowCount = savedData.length + 1;
    }
  }

  // إضافة البيانات للجدول وحفظها في LocalStorage
  document.getElementById('dataForm').addEventListener('submit', function (e) {
    e.preventDefault();
    const installation = document.getElementById('installation').value;
    const tankNumber = document.getElementById('tankNumber').value;
    const carType = document.getElementById('carType').value;
    const serialNumber = document.getElementById('serialNumber').value;
    const carRegistration = document.getElementById('carRegistration').value;
    const ownerName = document.getElementById('ownerName').value;

    // إضافة البيانات للجدول
    const table = document.getElementById('dataTable').querySelector('tbody');
    const row = table.insertRow();
    row.innerHTML = `
      <td>${installation}</td>
      <td>${tankNumber}</td>
      <td>${carType}</td>
      <td>${serialNumber}</td>
      <td>${carRegistration}</td>
      <td>${ownerName}</td>
      <td>${rowCount++}</td>
      <td>
        <div class="button-group">
          <button onclick="editRow(${rowCount - 1})"><i class="fa fa-edit"></i> تعديل</button>
          <button onclick="deleteRow(${rowCount - 1})"><i class="fa fa-trash"></i> حذف</button>
        </div>
      </td>
    `;

    // حفظ البيانات في LocalStorage
    const savedData = JSON.parse(localStorage.getItem('carData')) || [];
    savedData.push({
      installation,
      tankNumber,
      carType,
      serialNumber,
      carRegistration,
      ownerName
    });
    localStorage.setItem('carData', JSON.stringify(savedData));

    // مسح البيانات المدخلة بعد الحفظ
    document.getElementById('dataForm').reset();
    alert('✔ تم إدخال البيانات بنجاح!');
  });

  // تعديل البيانات في الجدول
  function editRow(index) {
    const savedData = JSON.parse(localStorage.getItem('carData'));
    const data = savedData[index];
    
    document.getElementById('installation').value = data.installation;
    document.getElementById('tankNumber').value = data.tankNumber;
    document.getElementById('carType').value = data.carType;
    document.getElementById('serialNumber').value = data.serialNumber;
    document.getElementById('carRegistration').value = data.carRegistration;
    document.getElementById('ownerName').value = data.ownerName;

    // حذف البيانات القديمة
    savedData.splice(index, 1);
    localStorage.setItem('carData', JSON.stringify(savedData));

    // إعادة تحميل البيانات
    loadData();
  }

  // حذف البيانات من الجدول
  function deleteRow(index) {
    if (confirm('هل أنت متأكد من أنك تريد حذف هذه البيانات؟')) {
      const savedData = JSON.parse(localStorage.getItem('carData'));
      savedData.splice(index, 1);
      localStorage.setItem('carData', JSON.stringify(savedData));

      // إعادة تحميل البيانات
      loadData();
    }
  }

  // طباعة الجدول
  function printTable() {
    const originalContent = document.body.innerHTML;
    const section = document.getElementById('tableSection').innerHTML;
    document.body.innerHTML = section;
    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
  }

  // تحميل البيانات عند تحميل الصفحة
  window.onload = loadData;
</script>

</body>
</html>
