<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة محطة الغاز - تسجيل الدخول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="icon" type="image/png" href="assets/future-fuel-icon (8).png">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #22c55e;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --bg-color: #ffffff;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            padding: 2rem;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            padding: 3rem;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }

        .logo-title {
            color: var(--text-color);
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .logo-subtitle {
            color: var(--text-color);
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .input-container {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            padding-right: 3rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .input-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-color);
            opacity: 0.5;
        }

        .login-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
        }

        .divider span {
            background: white;
            padding: 0 1rem;
            color: var(--text-color);
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .support-links {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .support-links a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: var(--light-color);
            border-radius: 8px;
            text-decoration: none;
            color: var(--text-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .support-links a:hover {
            background: white;
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .version-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .version-info p {
            font-size: 0.8rem;
            color: var(--text-color);
            opacity: 0.7;
            margin-bottom: 0.25rem;
        }

        .demo-credentials {
            background: var(--light-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
        }

        .demo-credentials h4 {
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .credential-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
            font-size: 0.8rem;
        }

        .credential-item .label {
            color: var(--text-color);
            opacity: 0.7;
        }

        .credential-item .value {
            color: var(--text-color);
            font-family: 'Courier New', monospace;
            font-weight: 600;
        }

        .quick-login-btn {
            width: 100%;
            padding: 0.5rem;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 0.5rem;
        }

        .quick-login-btn:hover {
            background: #16a34a;
        }

        .error-message {
            background: #fef2f2;
            color: var(--danger-color);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #fecaca;
            display: none;
            font-size: 0.9rem;
        }

        .success-message {
            background: #f0fdf4;
            color: var(--success-color);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #bbf7d0;
            display: none;
            font-size: 0.9rem;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Animation */
        .login-container {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Logo Section -->
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-gas-pump"></i>
            </div>
            <h1 class="logo-title">نظام إدارة محطة الغاز</h1>
            <p class="logo-subtitle">تسجيل الدخول للنظام الرئيسي</p>
        </div>

        <!-- Error/Success Messages -->
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <!-- Demo Credentials -->
        <div class="demo-credentials">
            <h4><i class="fas fa-info-circle"></i> بيانات تجريبية:</h4>
            <div class="credential-item">
                <span class="label">المستخدم:</span>
                <span class="value">admin</span>
            </div>
            <div class="credential-item">
                <span class="label">كلمة المرور:</span>
                <span class="value">admin123</span>
            </div>
            <button class="quick-login-btn" onclick="quickLogin()">
                <i class="fas fa-bolt"></i>
                تسجيل دخول سريع
            </button>
        </div>

        <!-- Login Form -->
        <form id="login-form">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <div class="input-container">
                    <input type="text" id="username" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <div class="input-container">
                    <input type="password" id="password" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
            </div>

            <button type="submit" class="login-button" id="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
            </button>
        </form>

        <!-- Divider -->
        <div class="divider">
            <span>أو</span>
        </div>

        <!-- Support Links -->
        <div class="support-links">
            <a href="#" onclick="requestActivation()">
                <i class="fas fa-key"></i>
                طلب تفعيل النظام
            </a>
            <a href="#" onclick="showSupport()">
                <i class="fas fa-headset"></i>
                الدعم الفني
            </a>
            <a href="license-login.html">
                <i class="fas fa-shield-alt"></i>
                لوحة التحكم في التراخيص
            </a>
        </div>

        <!-- Version Info -->
        <div class="version-info">
            <p>نظام إدارة محطة الغاز v2.0</p>
            <p>&copy; 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script src="scripts/login.js"></script>
</body>
</html>
