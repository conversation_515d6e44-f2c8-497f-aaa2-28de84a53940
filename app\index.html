<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة محطة الغاز - تسجيل الدخول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="icon" type="image/png" href="assets/future-fuel-icon (8).png">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #22c55e;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --bg-color: #ffffff;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            padding: 2rem;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .login-visual {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: white;
            text-align: center;
        }

        .visual-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .visual-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .visual-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            line-height: 1.6;
        }

        .login-form-container {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .form-header h1 {
            color: var(--text-color);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: var(--text-color);
            opacity: 0.7;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .input-container {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            padding-right: 3rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .input-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-color);
            opacity: 0.5;
        }

        .login-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .quick-access {
            background: var(--light-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .quick-access h3 {
            color: var(--text-color);
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .quick-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
        }

        .quick-btn {
            padding: 0.75rem;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-color);
        }

        .quick-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }

        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fef2f2;
            color: var(--danger-color);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #fecaca;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            color: var(--success-color);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #bbf7d0;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 400px;
            }

            .login-visual {
                display: none;
            }

            .login-form-container {
                padding: 2rem;
            }

            .quick-buttons {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        .login-container {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Visual Side -->
        <div class="login-visual">
            <div class="visual-icon">
                <i class="fas fa-gas-pump"></i>
            </div>
            <h2 class="visual-title">نظام إدارة محطة الغاز</h2>
            <p class="visual-subtitle">
                نظام شامل لإدارة ورشات الغاز
                <br>
                مع أدوات متقدمة وتقارير تفصيلية
            </p>
        </div>

        <!-- Form Side -->
        <div class="login-form-container">
            <div class="form-header">
                <h1>تسجيل الدخول</h1>
                <p>أدخل بياناتك للوصول إلى النظام</p>
            </div>

            <!-- Error/Success Messages -->
            <div id="error-message" class="error-message"></div>
            <div id="success-message" class="success-message"></div>

            <!-- Login Form -->
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <div class="input-container">
                        <input type="text" id="username" placeholder="أدخل اسم المستخدم" required>
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <div class="input-container">
                        <input type="password" id="password" placeholder="أدخل كلمة المرور" required>
                        <i class="fas fa-lock input-icon"></i>
                    </div>
                </div>

                <button type="submit" class="login-button" id="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- Forgot Password -->
            <div class="forgot-password">
                <a href="#" onclick="showSupport()">نسيت كلمة المرور؟</a>
            </div>

            <!-- Quick Access -->
            <div class="quick-access">
                <h3><i class="fas fa-bolt"></i> وصول سريع</h3>
                <div class="quick-buttons">
                    <button class="quick-btn" onclick="quickLogin('admin')">
                        <i class="fas fa-user-shield"></i><br>
                        مدير تجريبي
                    </button>
                    <button class="quick-btn" onclick="quickLogin('user')">
                        <i class="fas fa-user"></i><br>
                        مستخدم تجريبي
                    </button>
                    <button class="quick-btn" onclick="requestActivation()">
                        <i class="fas fa-key"></i><br>
                        طلب تفعيل
                    </button>
                    <button class="quick-btn" onclick="openLicensePanel()">
                        <i class="fas fa-shield-alt"></i><br>
                        لوحة التحكم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات تسجيل الدخول
        const users = {
            admin: { password: 'admin123', name: 'مدير النظام', role: 'admin' },
            user: { password: 'user123', name: 'مستخدم عادي', role: 'user' }
        };

        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        // معالجة تسجيل الدخول
        function handleLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');

            // التحقق من البيانات
            if (!username || !password) {
                showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            // عرض مؤشر التحميل
            setLoadingState(loginBtn, true);

            // محاكاة تأخير الشبكة
            setTimeout(() => {
                if (users[username] && users[username].password === password) {
                    // حفظ بيانات الجلسة
                    const session = {
                        workshopId: 'main-system',
                        userId: username,
                        username: username,
                        name: users[username].name,
                        role: users[username].role,
                        loginTime: new Date().toISOString(),
                        isActive: true
                    };

                    sessionStorage.setItem('gasSystemSession', JSON.stringify(session));

                    showMessage('تم تسجيل الدخول بنجاح! جاري إعادة التوجيه...', 'success');

                    setTimeout(() => {
                        window.location.href = 'main.html';
                    }, 1500);
                } else {
                    showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
                    setLoadingState(loginBtn, false);
                }
            }, 1000);
        }

        // دخول سريع
        function quickLogin(userType) {
            if (userType === 'admin') {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin123';
            } else if (userType === 'user') {
                document.getElementById('username').value = 'user';
                document.getElementById('password').value = 'user123';
            }

            setTimeout(() => {
                handleLogin();
            }, 500);
        }

        // طلب تفعيل
        function requestActivation() {
            alert('لطلب تفعيل النظام، يرجى التواصل مع الدعم الفني:\n\nالهاتف: +213 555 123 456\nالبريد: <EMAIL>');
        }

        // فتح لوحة التحكم
        function openLicensePanel() {
            window.open('license-login.html', '_blank');
        }

        // الدعم الفني
        function showSupport() {
            alert(`الدعم الفني:

📞 الهاتف: +213 555 123 456
📧 البريد: <EMAIL>
🕒 ساعات العمل: السبت - الخميس (8:00 - 17:00)
🌐 الدعم الفني: متاح 24/7

💡 للحصول على كلمة مرور جديدة أو المساعدة في استخدام النظام`);
        }

        // تعيين حالة التحميل للزر
        function setLoadingState(button, isLoading) {
            if (isLoading) {
                button.disabled = true;
                const originalContent = button.innerHTML;
                button.setAttribute('data-original-content', originalContent);
                button.innerHTML = '<div class="loading"></div> جاري تسجيل الدخول...';
            } else {
                button.disabled = false;
                const originalContent = button.getAttribute('data-original-content');
                if (originalContent) {
                    button.innerHTML = originalContent;
                }
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const errorDiv = document.getElementById('error-message');
            const successDiv = document.getElementById('success-message');
            
            // إخفاء جميع الرسائل
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            // عرض الرسالة المناسبة
            if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            } else if (type === 'success') {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';
            }, 3000);
        }

        // التحقق من الجلسة الموجودة
        window.addEventListener('load', function() {
            const session = sessionStorage.getItem('gasSystemSession');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    if (sessionData.isActive) {
                        showMessage('تم العثور على جلسة نشطة، جاري إعادة التوجيه...', 'success');
                        setTimeout(() => {
                            window.location.href = 'main.html';
                        }, 1000);
                    }
                } catch (error) {
                    sessionStorage.removeItem('gasSystemSession');
                }
            }
        });
    </script>
</body>
</html>
